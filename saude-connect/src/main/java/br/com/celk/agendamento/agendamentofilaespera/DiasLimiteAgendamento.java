package br.com.celk.agendamento.agendamentofilaespera;

import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.vo.basico.Empresa;

import java.io.Serializable;

public abstract class DiasLimiteAgendamento implements Serializable {

    private final Empresa empresa;

    public DiasLimiteAgendamento(Empresa empresa) {
        this.empresa = empresa;
    }

    public abstract boolean isDataAgendaMenorDataLimite(AgendaGradeAtendimentoDTO dto);

    public Empresa getEmpresa() {
        return empresa;
    }
}
