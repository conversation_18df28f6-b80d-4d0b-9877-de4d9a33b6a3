package br.com.celk.cscidadao.integracao.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class TfdIntegrationDTO extends DefaultDTO implements Serializable {

    private String nomePaciente;
    private String cnsPaciente;
    private String estabelecimentoLaudo;
    private String idRegistroClientUnidade;
    private String profissionalLaudo;
    private String procedimentoLaudo;
    private String dataLaudo;
    private String situacaoLaudo;
    private String idPedidoTfd;
    private String dataPedido;
    private String parecerPedido;
    private String dataAgendamento;
    private String destino;
    
    private Long codigoUsuarioCadSus;
    
    @JsonIgnore
    public Long getCodigoUsuarioCadSus() {
        return codigoUsuarioCadSus;
    }

    @JsonIgnore
    public void setCodigoUsuarioCadSus(Long codigoUsuarioCadSus) {
        this.codigoUsuarioCadSus = codigoUsuarioCadSus;
    }

    public String getIdRegistroClientUnidade() {
        return idRegistroClientUnidade;
    }

    public void setIdRegistroClientUnidade(String idRegistroClientUnidade) {
        this.idRegistroClientUnidade = idRegistroClientUnidade;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public String getCnsPaciente() {
        return cnsPaciente;
    }

    public void setCnsPaciente(String cnsPaciente) {
        this.cnsPaciente = cnsPaciente;
    }

    public String getEstabelecimentoLaudo() {
        return estabelecimentoLaudo;
    }

    public void setEstabelecimentoLaudo(String estabelecimentoLaudo) {
        this.estabelecimentoLaudo = estabelecimentoLaudo;
    }

    public String getProfissionalLaudo() {
        return profissionalLaudo;
    }

    public void setProfissionalLaudo(String profissionalLaudo) {
        this.profissionalLaudo = profissionalLaudo;
    }

    public String getProcedimentoLaudo() {
        return procedimentoLaudo;
    }

    public void setProcedimentoLaudo(String procedimentoLaudo) {
        this.procedimentoLaudo = procedimentoLaudo;
    }

    public String getDataLaudo() {
        return dataLaudo;
    }

    public void setDataLaudo(String dataLaudo) {
        this.dataLaudo = dataLaudo;
    }

    public String getSituacaoLaudo() {
        if (LaudoTfd.StatusLaudoTfd.ENCAMINHADO_MEDICO.value().toString().equals(this.situacaoLaudo)) {
            return Bundle.getStringApplication("rotulo_encaminhado_medico_abv");
        } else if (LaudoTfd.StatusLaudoTfd.RECEBIDO.value().toString().equals(this.situacaoLaudo)) {
            return Bundle.getStringApplication("rotulo_recebido");
        } else if (LaudoTfd.StatusLaudoTfd.ENCAMINHADO_REGIONAL.value().toString().equals(this.situacaoLaudo)) {
            return Bundle.getStringApplication("rotulo_encaminhado_regional_abv");
        } else if (LaudoTfd.StatusLaudoTfd.AUTORIZADO.value().toString().equals(this.situacaoLaudo)) {
            return Bundle.getStringApplication("rotulo_autorizado");
        } else if (LaudoTfd.StatusLaudoTfd.NEGADO.value().toString().equals(this.situacaoLaudo)) {
            return Bundle.getStringApplication("rotulo_negado");
        } else if (LaudoTfd.StatusLaudoTfd.INCONCLUSIVO.value().toString().equals(this.situacaoLaudo)) {
            return Bundle.getStringApplication("rotulo_inconclusivo");
        } else if (LaudoTfd.StatusLaudoTfd.CANCELADO.value().toString().equals(this.situacaoLaudo)) {
            return Bundle.getStringApplication("rotulo_cancelado");
        }
        return Bundle.getStringApplication("rotulo_desconhecido");
    }

    public void setSituacaoLaudo(String situacaoLaudo) {
        this.situacaoLaudo = situacaoLaudo;
    }

    public String getIdPedidoTfd() {
        return idPedidoTfd;
    }

    public void setIdPedidoTfd(String idPedidoTfd) {
        this.idPedidoTfd = idPedidoTfd;
    }

    public String getDataPedido() {
        return dataPedido;
    }

    public void setDataPedido(String dataPedido) {
        this.dataPedido = dataPedido;
    }

    public String getParecerPedido() {
        for (PedidoTfd.Parecer parecer : PedidoTfd.Parecer.values()) {
            if (parecer.value().toString().equals(this.parecerPedido)) {
                return parecer.name();
            }
        }
        return null;
    }

    public void setParecerPedido(String parecerPedido) {
        this.parecerPedido = parecerPedido;
    }

    public String getDataAgendamento() {
        return dataAgendamento;
    }

    public void setDataAgendamento(String dataAgendamento) {
        this.dataAgendamento = dataAgendamento;
    }

    public String getDestino() {
        return destino;
    }

    public void setDestino(String destino) {
        this.destino = destino;
    }

    @JsonIgnore
    @Override
    public String toString() {
        return "Código: " + getIdRegistroClient() + ", paciente: " + getNomePaciente() + ", data do pedido: " + getDataPedido() + ", situação: " + getSituacaoLaudo() + ", estabelecimento do laudo:" + getEstabelecimentoLaudo()
                + ", destino:" + getDestino();
    }
    
}
