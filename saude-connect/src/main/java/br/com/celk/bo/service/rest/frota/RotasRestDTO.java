package br.com.celk.bo.service.rest.frota;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class RotasRestDTO implements Serializable {

    private Long codigo;
    private Double latitude;
    private Double longitude;
    private Double velocidade;
    private String motorista;
    private String veiculo;
    @JsonSerialize(using = DateTimeRestSerializer.class)
    @JsonDeserialize(using = DateTimeRestDeserializer.class)
    private Date dataDoRegistro;

    public Date getDataDoRegistro() {
        return dataDoRegistro;
    }

    public void setDataDoRegistro(Date dataDoRegistro) {
        this.dataDoRegistro = dataDoRegistro;
    }

    public void setVeiculo(String veiculo) {
        this.veiculo = veiculo;
    }

    public String getVeiculo() {
        return veiculo;
    }
    private Long usuario;

    public String getMotorista() {
        return motorista;
    }

    public void setMotorista(String motorista) {
        this.motorista = motorista;
    }

    public Long getUsuario() {
        return usuario;
    }

    public void setUsuario(Long usuario) {
        this.usuario = usuario;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getVelocidade() {
        return velocidade;
    }

    public void setVelocidade(Double velocidade) {
        this.velocidade = velocidade;
    }

}
