package br.com.celk.solicitacao;

import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;

public class ValidarDevolucaoSolicitacao implements ValidacaoStatusSolicitacaoAgendamento {

    @Override
    public void validar(SolicitacaoAgendamento solicitacaoAgendamento) throws ValidacaoException {
        if (!solicitacaoAgendamento.isStatusRegulacaoPendente()) {
            this.exceptionDefault(solicitacaoAgendamento);
        }
    }
}
