package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.SetorVigilancia;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class RelatorioAlvarasDTOParam implements Serializable {

    private AtividadeEstabelecimento atividade;
    private SetorVigilancia setorVigilancia;
    private Estabelecimento estabelecimento;
    private String vencidos;
    private String bairro;
    private DatePeriod periodo;
    private Long situacao;
    private FormaApresentacao formaApresentacao;
    private Long tipoRelatorio;
    private Long tipoAlvara;

    public enum TipoAlvara implements IEnum<TipoAlvara> {

        AUTORIZACAO_SANITARIA(4L, Bundle.getStringApplication("rotulo_autorizacao_sanitaria")),
        ALVARA_SANITARIO(0L, Bundle.getStringApplication("rotulo_alvara_sanitario")),
        ALVARA_EVENTOS(1L, Bundle.getStringApplication("rotulo_alvara_participantes_evento")),
        LICENCA_SANITARIA_TRANSPORTE(2L, Bundle.getStringApplication("rotulo_licenca_sanitaria_para_transporte")),
        HABITE_SE_SANITARIO(3L, Bundle.getStringApplication("rotulo_habitese"));

        private Long value;
        private String descricao;

        TipoAlvara(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static TipoAlvara valueOf(Long value) {
            for (TipoAlvara tipoAlvara : TipoAlvara.values()) {
                if (tipoAlvara.value().equals(value)) {
                    return tipoAlvara;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public enum TipoRelatorio implements IEnum<TipoRelatorio> {

        DETALHADO(1L, Bundle.getStringApplication("rotulo_detalhado")),
        RESUMIDO(0L, Bundle.getStringApplication("rotulo_resumido"));

        private Long value;
        private String descricao;

        private TipoRelatorio(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static TipoRelatorio valueOf(Long value) {
            for (TipoRelatorio tipoRelatorio : TipoRelatorio.values()) {
                if (tipoRelatorio.value().equals(value)) {
                    return tipoRelatorio;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public enum FormaApresentacao {

        GERAL(Bundle.getStringApplication("rotulo_geral")),
        SITUACAO(Bundle.getStringApplication("rotulo_situacao")),
        ATIVIDADE(Bundle.getStringApplication("rotulo_atividade_cnae"));

        private final String descricao;

        private FormaApresentacao(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public static enum Situacao implements IEnum<Situacao> {

        PENDENTE(9L, Bundle.getStringApplication("rotulo_pendente")),
        FINALIZADO(8L, Bundle.getStringApplication("rotulo_finalizado")),
        DEFERIDO(2L, Bundle.getStringApplication("rotulo_deferido")),
        INDEFERIDO(3L, Bundle.getStringApplication("rotulo_indeferido")),
        CANCELADO(7L, Bundle.getStringApplication("rotulo_cancelado")),;
        private Long value;
        private String descricao;

        private Situacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static RequerimentoVigilancia.Situacao valeuOf(Long value) {
            for (RequerimentoVigilancia.Situacao situacao : RequerimentoVigilancia.Situacao.values()) {
                if (situacao.value().equals(value)) {
                    return situacao;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return this.descricao;
        }
    }

    @DescricaoParametro("rotulo_bairro")
    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_vencidos")
    public String getDescricaoVencidos() {
        if (RepositoryComponentDefault.SIM.equals(getVencidos())) {
            return Bundle.getStringApplication("rotulo_sim");
        } else if (RepositoryComponentDefault.NAO.equals(getVencidos())) {
            return Bundle.getStringApplication("rotulo_nao");
        }

        return Bundle.getStringApplication("rotulo_ambos");
    }

    public String getVencidos() {
        return vencidos;
    }

    public void setVencidos(String vencidos) {
        this.vencidos = vencidos;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_atividade")
    public AtividadeEstabelecimento getAtividade() {
        return atividade;
    }

    public void setAtividade(AtividadeEstabelecimento atividade) {
        this.atividade = atividade;
    }

    @DescricaoParametro("rotulo_setor_responsavel")
    public SetorVigilancia getSetorVigilancia() {
        return setorVigilancia;
    }

    public void setSetorVigilancia(SetorVigilancia setorVigilancia) {
        this.setorVigilancia = setorVigilancia;
    }

    @DescricaoParametro("rotulo_estabelecimento")
    public Estabelecimento getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(Estabelecimento estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    @DescricaoParametro("rotulo_situacao")
    public String getDescricaoSituacao() {
        return getSituacao()!=null?Situacao.valeuOf(getSituacao()).descricao():"";
    }

    public Long getTipoRelatorio() {
        return tipoRelatorio;
    }

    public void setTipoRelatorio(Long tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }

    public Long getTipoAlvara() {
        return tipoAlvara;
    }

    public void setTipoAlvara(Long tipoAlvara) {
        this.tipoAlvara = tipoAlvara;
    }
}
