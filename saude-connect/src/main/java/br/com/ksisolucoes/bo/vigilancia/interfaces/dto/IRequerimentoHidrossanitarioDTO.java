package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoArquitetonicoSanitario;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitario;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitario;

public interface IRequerimentoHidrossanitarioDTO {

    RequerimentoProjetoHidrossanitario getRequerimentoProjetoHidrossanitario();
    RequerimentoVistoriaHidrossanitario getRequerimentoVistoriaHidrossanitario();

    RequerimentoProjetoArquitetonicoSanitario getRequerimentoProjetoArquitetonicoSanitario();
}
