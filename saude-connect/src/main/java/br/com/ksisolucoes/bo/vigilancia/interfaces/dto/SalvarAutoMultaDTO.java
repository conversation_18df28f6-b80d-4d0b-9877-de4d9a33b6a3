package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMultaFiscal;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarAutoMultaDTO implements Serializable {

    private AutoMulta autoMulta;
    private List<AutoMultaFiscal> lstFiscal;
    private PnlRequerimentoVigilanciaAnexoDTO anexoDTO;

    public AutoMulta getAutoMulta() {
        return autoMulta;
    }

    public void setAutoMulta(AutoMulta autoMulta) {
        this.autoMulta = autoMulta;
    }

    public List<AutoMultaFiscal> getLstFiscal() {
        return lstFiscal;
    }

    public void setLstFiscal(List<AutoMultaFiscal> lstFiscal) {
        this.lstFiscal = lstFiscal;
    }

    public PnlRequerimentoVigilanciaAnexoDTO getAnexoDTO() {
        return anexoDTO;
    }

    public void setAnexoDTO(PnlRequerimentoVigilanciaAnexoDTO anexoDTO) {
        this.anexoDTO = anexoDTO;
    }
}
