package br.com.ksisolucoes.report.agendamento.dto;

import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class RelacaoAgendasUnidadeDTO implements Serializable {
    private Empresa unidade;
    private Empresa unidadeExecutante;
    private TipoProcedimento tipoProcedimento;
    private Profissional profissional;
    private Double vagasPeriodo;
    private Long vagasUtilizadas;
    private Date data;
    private Date horaInicial;
    private TipoAtendimentoAgenda tipoAtendimentoAgenda;

    public TipoAtendimentoAgenda getTipoAtendimentoAgenda() {
        return tipoAtendimentoAgenda;
    }

    public void setTipoAtendimentoAgenda(TipoAtendimentoAgenda tipoAtendimentoAgenda) {
        this.tipoAtendimentoAgenda = tipoAtendimentoAgenda;
    }

    public Date getHoraInicial() {
        return horaInicial;
    }

    public void setHoraInicial(Date horaInicial) {
        this.horaInicial = horaInicial;
    }

    public Date getData() {
        return data;
    }

    public void setData(Date data) {
        this.data = data;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public TipoProcedimento getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(TipoProcedimento tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    public Empresa getUnidade() {
        return unidade;
    }

    public void setUnidade(Empresa unidade) {
        this.unidade = unidade;
    }

    public Empresa getUnidadeExecutante() {
        return unidadeExecutante;
    }

    public void setUnidadeExecutante(Empresa unidadeExecutante) {
        this.unidadeExecutante = unidadeExecutante;
    }

    public Double getVagasPeriodo() {
        return vagasPeriodo;
    }

    public void setVagasPeriodo(Double vagasPeriodo) {
        this.vagasPeriodo = vagasPeriodo;
    }

    public Long getVagasUtilizadas() {
        return vagasUtilizadas;
    }

    public void setVagasUtilizadas(Long vagasUtilizadas) {
        this.vagasUtilizadas = vagasUtilizadas;
    }

    public Long getVagasPeriodoLong(){
        Long valueL = this.vagasPeriodo.longValue();
        if (this.vagasPeriodo.compareTo(0D) <= 0) {
            valueL = 0L;
        } else if (this.vagasPeriodo.compareTo(1D) <= 0) {
            valueL = 1L;
        }

        return valueL;
    }
}
