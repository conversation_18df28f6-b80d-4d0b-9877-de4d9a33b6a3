package br.com.celk.sms.restservice.response;

import br.com.celk.sms.restservice.util.EnumUtil;

import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class ResponseRecipient implements Serializable {

    private String id; //id da mensagem
    private OperatorCode operatorCode; //codigo de retorno da operadora
    private String destination; //numero que a mensagem foi enviada (send)
    private Date data; //data (varios processos)
    private String from; //numero que recebeu a mensavem (received)
    private String to; //numero que a operadora está recebendo a resposta do cliente (received)
    private String mensagem; //mensagem (varios processos)

    public ResponseRecipient() {}

    public ResponseRecipient(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public OperatorCode getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String status) {
        if(status != null && !status.isEmpty()) {
            this.operatorCode = EnumUtil.resolveValue(OperatorCode.values(), Integer.parseInt(status));
        }
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public Date getData() {
        return data;
    }

    public void setData(String data) {
        try {
            this.data = data == null ? null : new SimpleDateFormat("yyyy-MM-dd HH:mm:ssX").parse(data);
        } catch (ParseException e) {
            this.data = null;
        }
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getTo() {
        return to;
    }

    public void setTo(String to) {
        this.to = to;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

}
