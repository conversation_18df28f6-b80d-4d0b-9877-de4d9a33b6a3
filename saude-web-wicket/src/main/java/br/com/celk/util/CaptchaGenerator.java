package br.com.celk.util;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.security.SecureRandom;
import java.util.Random;

/**
 * Gerador de CAPTCHA para prevenir acesso automatizado de bots
 * 
 * <AUTHOR>
 */
public class CaptchaGenerator {
    
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final int CAPTCHA_LENGTH = 5;
    private static final int IMAGE_WIDTH = 200;
    private static final int IMAGE_HEIGHT = 60;
    private static final Random random = new SecureRandom();
    
    /**
     * Gera um código CAPTCHA aleatório
     * 
     * @return String com o código CAPTCHA
     */
    public static String generateCaptchaCode() {
        StringBuilder captcha = new StringBuilder();
        for (int i = 0; i < CAPTCHA_LENGTH; i++) {
            captcha.append(CHARACTERS.charAt(random.nextInt(CHARACTERS.length())));
        }
        return captcha.toString();
    }
    
    /**
     * Gera uma imagem CAPTCHA baseada no código fornecido
     * 
     * @param captchaCode Código do CAPTCHA
     * @return BufferedImage com a imagem do CAPTCHA
     */
    public static BufferedImage generateCaptchaImage(String captchaCode) {
        BufferedImage image = new BufferedImage(IMAGE_WIDTH, IMAGE_HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // Configurar renderização
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // Fundo branco
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, IMAGE_WIDTH, IMAGE_HEIGHT);
        
        // Adicionar ruído de fundo
        addNoise(g2d);
        
        // Desenhar o texto do CAPTCHA
        drawCaptchaText(g2d, captchaCode);
        
        // Adicionar linhas de interferência
        addInterferenceLines(g2d);
        
        g2d.dispose();
        return image;
    }
    
    /**
     * Adiciona ruído de fundo à imagem
     */
    private static void addNoise(Graphics2D g2d) {
        g2d.setColor(new Color(200, 200, 200));
        for (int i = 0; i < 50; i++) {
            int x = random.nextInt(IMAGE_WIDTH);
            int y = random.nextInt(IMAGE_HEIGHT);
            g2d.fillOval(x, y, 2, 2);
        }
    }
    
    /**
     * Desenha o texto do CAPTCHA com distorções
     */
    private static void drawCaptchaText(Graphics2D g2d, String captchaCode) {
        Font[] fonts = {
            new Font("Arial", Font.BOLD, 24),
            new Font("Times New Roman", Font.BOLD, 24),
            new Font("Courier New", Font.BOLD, 24)
        };
        
        int x = 20;
        for (int i = 0; i < captchaCode.length(); i++) {
            // Cor aleatória para cada caractere
            g2d.setColor(new Color(
                random.nextInt(100), 
                random.nextInt(100), 
                random.nextInt(100)
            ));
            
            // Fonte aleatória
            g2d.setFont(fonts[random.nextInt(fonts.length)]);
            
            // Rotação leve
            double angle = (random.nextDouble() - 0.5) * 0.5; // -0.25 a 0.25 radianos
            g2d.rotate(angle);
            
            // Posição com variação vertical
            int y = 35 + random.nextInt(10);
            g2d.drawString(String.valueOf(captchaCode.charAt(i)), x, y);
            
            // Resetar rotação
            g2d.rotate(-angle);
            
            x += 30;
        }
    }
    
    /**
     * Adiciona linhas de interferência
     */
    private static void addInterferenceLines(Graphics2D g2d) {
        g2d.setColor(new Color(150, 150, 150));
        g2d.setStroke(new BasicStroke(2));
        
        for (int i = 0; i < 3; i++) {
            int x1 = random.nextInt(IMAGE_WIDTH);
            int y1 = random.nextInt(IMAGE_HEIGHT);
            int x2 = random.nextInt(IMAGE_WIDTH);
            int y2 = random.nextInt(IMAGE_HEIGHT);
            g2d.drawLine(x1, y1, x2, y2);
        }
    }
    
    /**
     * Valida se o código informado pelo usuário está correto
     * 
     * @param userInput Código informado pelo usuário
     * @param correctCode Código correto do CAPTCHA
     * @return true se o código está correto
     */
    public static boolean validateCaptcha(String userInput, String correctCode) {
        if (userInput == null || correctCode == null) {
            return false;
        }
        return userInput.trim().toUpperCase().equals(correctCode.toUpperCase());
    }
}
