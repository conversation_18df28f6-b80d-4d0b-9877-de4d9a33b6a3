package br.com.celk.util;

import org.apache.wicket.Session;

/**
 * Utilitário para gerenciar dados do CAPTCHA na sessão do usuário
 * 
 * <AUTHOR>
 */
public class CaptchaSession {
    
    private static final String CAPTCHA_CODE_KEY = "captcha.code";
    private static final String CAPTCHA_TIMESTAMP_KEY = "captcha.timestamp";
    private static final long CAPTCHA_TIMEOUT = 5 * 60 * 1000; // 5 minutos
    
    /**
     * Armazena o código CAPTCHA na sessão
     * 
     * @param captchaCode Código do CAPTCHA
     */
    public static void storeCaptchaCode(String captchaCode) {
        Session.get().setAttribute(CAPTCHA_CODE_KEY, captchaCode);
        Session.get().setAttribute(CAPTCHA_TIMESTAMP_KEY, System.currentTimeMillis());
    }
    
    /**
     * Recupera o código CAPTCHA da sessão
     * 
     * @return Código do CAPTCHA ou null se não existir ou expirado
     */
    public static String getCaptchaCode() {
        Long timestamp = (Long) Session.get().getAttribute(CAPTCHA_TIMESTAMP_KEY);
        if (timestamp == null || (System.currentTimeMillis() - timestamp) > CAPTCHA_TIMEOUT) {
            // CAPTCHA expirado, limpar da sessão
            clearCaptcha();
            return null;
        }
        return (String) Session.get().getAttribute(CAPTCHA_CODE_KEY);
    }
    
    /**
     * Valida o código CAPTCHA informado pelo usuário
     * 
     * @param userInput Código informado pelo usuário
     * @return true se o código está correto e não expirado
     */
    public static boolean validateCaptcha(String userInput) {
        String storedCode = getCaptchaCode();
        if (storedCode == null) {
            return false;
        }
        
        boolean isValid = CaptchaGenerator.validateCaptcha(userInput, storedCode);
        
        // Limpar o CAPTCHA da sessão após validação (uso único)
        if (isValid) {
            clearCaptcha();
        }
        
        return isValid;
    }
    
    /**
     * Limpa os dados do CAPTCHA da sessão
     */
    public static void clearCaptcha() {
        Session.get().removeAttribute(CAPTCHA_CODE_KEY);
        Session.get().removeAttribute(CAPTCHA_TIMESTAMP_KEY);
    }
    
    /**
     * Gera um novo CAPTCHA e armazena na sessão
     * 
     * @return Código do novo CAPTCHA
     */
    public static String generateNewCaptcha() {
        String newCode = CaptchaGenerator.generateCaptchaCode();
        storeCaptchaCode(newCode);
        return newCode;
    }
}
