package br.com.celk.view.materiais.produtosporunidade;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.basico.empresa.pnl.PnlConsultaEmpresa;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.EstoqueEmpresaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresaPK;
import br.com.ksisolucoes.vo.entradas.estoque.Localizacao;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlCopiarEstoqueEmpresaPorUnidade extends Panel {

    private CompoundPropertyModel<List<Empresa>> dialogModel;
    private Form form;
    private AbstractAjaxButton btnFechar;
    private AbstractAjaxButton btnAdicionar;
    private AbstractAjaxButton btnCopiar;
    private AbstractAjaxButton btnLimpar;
    private Table<Empresa> table;
    private List<Empresa> lstEmpresasDestino = new ArrayList<Empresa>();
    private PnlConsultaEmpresa pnlConsultaEmpresaOrigem;
    private PnlConsultaEmpresa pnlConsultaEmpresaDestino;
    private Empresa empresaDestino;
    private Empresa empresaOrigem;
    private List<EstoqueEmpresa> lstEstoqueEmpresa;

    public PnlCopiarEstoqueEmpresaPorUnidade(String id) {
        super(id);
        createDialogDetalhes();
    }

    private void createDialogDetalhes() {

        setOutputMarkupId(true);

        dialogModel = new CompoundPropertyModel(new ArrayList<Empresa>());

        form = new Form("form", dialogModel);

        form.setOutputMarkupId(true);

        form.add(pnlConsultaEmpresaOrigem = new PnlConsultaEmpresa("unidadeOrigem", new PropertyModel<Empresa>(this, "empresaOrigem")).setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_ODONTO, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MEDICAMENTO, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MATERIAL,Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO,Empresa.TIPO_ESTABELECIMENTO_FARMACIA)));
        form.add(pnlConsultaEmpresaDestino = new PnlConsultaEmpresa("unidadeDestino", new PropertyModel<Empresa>(this, "empresaDestino")).setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_ODONTO, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MEDICAMENTO, Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MATERIAL,Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO,Empresa.TIPO_ESTABELECIMENTO_FARMACIA)));
        form.add(table = new Table<Empresa>("table", getColumns(), getCollectionProvider()));
        form.add(btnAdicionar = new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                addEmpresa(target);
            }
        });

        form.add(btnCopiar = new AbstractAjaxButton("btnCopiar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                copiar(target);
            }
        });

        form.add(btnLimpar = new AbstractAjaxButton("btnLimpar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limpar(target);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limpar(target);
                onFechar(target);
            }
        });

        table.populate();

        add(form);


        btnFechar.setDefaultFormProcessing(false);

    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void update(AjaxRequestTarget target) {
        target.add(form);
    }

    private List<ISortableColumn<Empresa>> getColumns() {
        List<ISortableColumn<Empresa>> columns = new ArrayList<ISortableColumn<Empresa>>();

        ColumnFactory columnFactory = new ColumnFactory(Empresa.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("unidade"), VOUtils.montarPath(Empresa.PROP_DESCRICAO_FORMATADO)));

        return columns;
    }

    private CustomColumn getCustomColumn() {
        return new CustomColumn<Empresa>() {
            @Override
            public Component getComponent(String componentId, final Empresa rowObject) {
                return new CrudActionsColumnPanel(componentId) {
                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        excluirEmpresa(target, rowObject);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    }

                    @Override
                    public boolean isConsultarVisible() {
                        return false;
                    }

                    @Override
                    public boolean isEditarVisible() {
                        return false;
                    }
                };
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstEmpresasDestino;
            }
        };
    }

    private void addEmpresa(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (empresaDestino == null) {
            throw new ValidacaoException(BundleManager.getString("informeUnidadeDestino"));
        }

        if (empresaOrigem == null) {
            throw new ValidacaoException(BundleManager.getString("informeUnidadeOrigem"));
        }

        if (pnlConsultaEmpresaOrigem.isEnabled()) {
            lstEstoqueEmpresa = LoadManager.getInstance(EstoqueEmpresa.class)
                    .addProperty(VOUtils.montarPath(EstoqueEmpresa.PROP_FLAG_ATIVO))
                    .addProperty(VOUtils.montarPath(EstoqueEmpresa.PROP_ESTOQUE_MINIMO))
                    .addProperty(VOUtils.montarPath(EstoqueEmpresa.PROP_QUANTIDADE_PADRAO_DISPENSACAO))
                    .addProperty(VOUtils.montarPath(EstoqueEmpresa.PROP_LOCALIZACAO, Localizacao.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(EstoqueEmpresa.PROP_LOCALIZACAO, Localizacao.PROP_DESCRICAO))
                    .addProperty(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO, Produto.PROP_DESCRICAO))
                    .addProperty(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA, Empresa.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
                    .addParameter(new QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), empresaOrigem))
                    .addParameter(new QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_FLAG_ATIVO), RepositoryComponentDefault.SIM))
                    .start().getList();

            if (lstEstoqueEmpresa.size() <= 0) {
                throw new ValidacaoException(BundleManager.getString("unidadeSelecionadaNaoPossuiProdutosParaSeremCopiados"));
            }
        }


        for (Empresa empresa : lstEmpresasDestino) {
            if (empresa.equals(empresaDestino)) {
                throw new ValidacaoException(BundleManager.getString("unidadeJaAdicionada"));
            }
        }
        lstEmpresasDestino.add(empresaDestino);
        table.update(target);
        pnlConsultaEmpresaOrigem.setEnabled(false);
        pnlConsultaEmpresaDestino.limpar(target);
        target.add(pnlConsultaEmpresaOrigem);

    }

    private void excluirEmpresa(AjaxRequestTarget target, Empresa empresa) {
        lstEmpresasDestino.remove(empresa);
        table.update(target);
    }

    private void copiar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (lstEmpresasDestino.size() <= 0) {
            throw new ValidacaoException(BundleManager.getString("informeAoMenosUmaEmpresaDestino"));
        }

        if (empresaOrigem == null) {
            throw new ValidacaoException(BundleManager.getString("informeUnidadeOrigem"));
        }

        BOFactoryWicket.getBO(EstoqueEmpresaFacade.class).copiarEstoqueUnidade(lstEstoqueEmpresa, lstEmpresasDestino);
        limpar(target);
        onFechar(target);
    }

    private void limpar(AjaxRequestTarget target) {
        pnlConsultaEmpresaOrigem.limpar(target);
        pnlConsultaEmpresaDestino.limpar(target);
        lstEmpresasDestino= new ArrayList<Empresa>();
        table.update(target);
        pnlConsultaEmpresaOrigem.setEnabled(true);
        target.add(pnlConsultaEmpresaOrigem);
        lstEstoqueEmpresa = null;
    }
}
