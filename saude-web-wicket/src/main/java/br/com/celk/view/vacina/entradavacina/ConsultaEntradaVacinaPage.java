package br.com.celk.view.vacina.entradavacina;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.vacina.entradavacina.customcolumn.EntradaVacinaColumnPanel;
import br.com.celk.view.vacina.entradavacina.customize.CustomizeConsultaEntradaVacina;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.vacina.EntradaVacina;
import br.com.ksisolucoes.vo.vacina.PedidoVacinaInsumo;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import br.com.ksisolucoes.bo.vacina.interfaces.facade.VacinaFacade;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.pessoa.autocomplete.AutoCompleteConsultaPessoa;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.basico.Empresa;
import java.util.Arrays;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Page;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaEntradaVacinaPage extends ConsultaPage<EntradaVacina, List<BuilderQueryCustom.QueryParameter>> {

    private InputField txtNota;
    
    private String nota;
    private Pessoa fornecedor;
    private List<Empresa> empresa;
    private String tipoData;
    private DatePeriod periodo;
    private Long situacao;
    
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DlgConfirmacao dlgConfirmacao;    
    
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(txtNota = new UpperField("nota"));
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa"));
        form.add(new AutoCompleteConsultaPessoa("fornecedor"));
        form.add(new PnlDatePeriod("periodo"));
        form.add(getDropDownTipoData());
        form.add(getDropDownSituacao());
        
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(true);
        autoCompleteConsultaEmpresa.setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE));

        setExibeExpandir(true);
    }
    
    private DropDown getDropDownSituacao(){
        DropDown dropDown = new DropDown("situacao");

        for (EntradaVacina.StatusEntradaVacina statusEntradaVacina : EntradaVacina.StatusEntradaVacina.values()) {
            dropDown.addChoice(statusEntradaVacina.getValue() , statusEntradaVacina.toString());
        }
        dropDown.addChoice(null, BundleManager.getString("todos"));

        return dropDown;
    }

    private DropDown getDropDownTipoData(){
        DropDown dropDown = new DropDown("tipoData");
        
        dropDown.addChoice(EntradaVacina.PROP_DATA_CADASTRO , BundleManager.getString("dataCadastro"));
        dropDown.addChoice(EntradaVacina.PROP_DATA_PORTARIA , BundleManager.getString("dataPortaria"));
        dropDown.addChoice(EntradaVacina.PROP_DATA_EMISSAO , BundleManager.getString("dataEmissao"));
        
        return dropDown;
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(EntradaVacina.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("nota"), VOUtils.montarPath(EntradaVacina.PROP_NOTA)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dataCadastro"), VOUtils.montarPath(EntradaVacina.PROP_DATA_CADASTRO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dataPortaria"), VOUtils.montarPath(EntradaVacina.PROP_DATA_PORTARIA)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("dataEmissao"), VOUtils.montarPath(EntradaVacina.PROP_DATA_EMISSAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), VOUtils.montarPath(EntradaVacina.PROP_STATUS),VOUtils.montarPath(EntradaVacina.PROP_DESCRICAO_SITUACAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("empresa"), VOUtils.montarPath(EntradaVacina.PROP_EMPRESA,Empresa.PROP_DESCRICAO_FORMATADO),VOUtils.montarPath(EntradaVacina.PROP_EMPRESA,Empresa.PROP_DESCRICAO)));
        
        return columns;
    }
    
    private CustomColumn getCustomColumn(){
        return new CustomColumn<EntradaVacina>() {

            @Override
            public Component getComponent(String componentId, final EntradaVacina rowObject) {
                return new EntradaVacinaColumnPanel(componentId, rowObject) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroEntradaVacinaPage(rowObject));
                    }

                    @Override
                    public void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(rowObject);
                        getPageableTable().update(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesEntradaVacinaPage(rowObject));
                    }

                    @Override
                    public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        final DetalhesEntradaVacinaPage detalhes = new DetalhesEntradaVacinaPage(rowObject);
                        AbstractAjaxLink btnConfirmar = null;
                        detalhes.getControls().add(btnConfirmar = new AbstractAjaxLink(detalhes.getControls().newChildId()) {
                            @Override
                            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                initDlgConfirmacao(target, detalhes, rowObject);
                                if (dlgConfirmacao!=null) {
                                    dlgConfirmacao.show(target);
                                }
                            }
                        });
                        btnConfirmar.add(new AttributeModifier("value", BundleManager.getString("confirmar")));
                        btnConfirmar.add(new AttributeModifier("class", "checkmark"));
                        
                        detalhes.getControls().add(btnConfirmar = new AbstractAjaxLink(detalhes.getControls().newChildId()) {
                            @Override
                            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                setResponsePage(new CadastroEntradaVacinaPage(rowObject));
                            }
                        });
                        btnConfirmar.add(new AttributeModifier("value", BundleManager.getString("editar")));
                        btnConfirmar.add(new AttributeModifier("class", "doc-edit"));
                        
                        setResponsePage(detalhes);
                    }

                    @Override
                    public void updateTable(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        getPageableTable().update(target);
                    }
                };
            }
        };
    }

    private void initDlgConfirmacao(AjaxRequestTarget target, BasePage parent,final EntradaVacina entradaVacina) {
        if (dlgConfirmacao == null) {
            parent.addModal(target, dlgConfirmacao = new DlgConfirmacao(parent.newModalId(), BundleManager.getString("confirmaEntradaNota") + "?") {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(VacinaFacade.class).confirmarEntradaVacina(entradaVacina);
                        Page page = null;
                    try {
                        page = (Page) ConsultaEntradaVacinaPage.class.newInstance();
                        setResponsePage(page);
                        ConsultaEntradaVacinaPage.this.getSession().getFeedbackMessages().info(page, BundleManager.getString("operacaoRealizadaComSucesso"));
                    } catch (InstantiationException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    } catch (IllegalAccessException ex) {
                        Loggable.log.error(ex.getMessage(), ex);
                    }
                }
            });
        }
    }
    
    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaEntradaVacina()){

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(EntradaVacina.PROP_DATA_CADASTRO, false);
            }
            
        };
    }

    @Override
    public List<QueryParameter> getParameters() {
        List<QueryParameter> parameters = new ArrayList<QueryParameter>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EntradaVacina.PROP_NOTA), QueryParameter.ILIKE, nota, BuilderQueryCustom.MatchMode.EXACT));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EntradaVacina.PROP_FORNECEDOR), fornecedor));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EntradaVacina.PROP_EMPRESA), QueryParameter.IN, empresa));
        if (periodo!=null) {
            parameters.add(new QueryCustom.QueryCustomParameter(tipoData, Data.adjustRangeHour(periodo)));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoVacinaInsumo.PROP_STATUS), situacao));
        
        return parameters;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtNota;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroEntradaVacinaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaRecebimentosVacinas");
    }

}
