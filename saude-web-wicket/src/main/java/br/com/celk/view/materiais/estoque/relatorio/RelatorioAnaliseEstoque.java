package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.centrocusto.pnl.PnlConsultaCentroCusto;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.empresa.pnl.PnlConsultaEmpresa;
import br.com.celk.view.basico.localizacao.autocomplete.AutoCompleteConsultaLocalizacao;
import br.com.celk.view.basico.localizacao.pnl.PnlConsultaLocalizacao;
import br.com.celk.view.materiais.centrocusto.autocomplete.AutoCompleteConsultaCentroCusto;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioAnaliseEstoqueDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.util.MapBean;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import java.util.Date;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;


/**
 *
 * <AUTHOR>
 */
@Private
public class RelatorioAnaliseEstoque extends RelatorioPage<RelatorioAnaliseEstoqueDTOParam> {
        
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaCentroCusto autoCompleteConsultaCentroCusto;
    private AutoCompleteConsultaLocalizacao autoCompleteConsultaLocalizacao;
    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private DropDown dropDownProdutoSemMovimentacao;
    private String mesAno;
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresas").setValidaUsuarioEmpresa(!isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA)));
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(autoCompleteConsultaLocalizacao = new AutoCompleteConsultaLocalizacao("localizacao"));
        form.add(autoCompleteConsultaCentroCusto = new AutoCompleteConsultaCentroCusto("centroCusto"));
        form.add(getDropDownCurva());
        form.add(new RequiredInputField("mesAno", new PropertyModel(this, "mesAno")));
        form.add(getDropDownOrdenacao());
        form.add(dropDownProdutoSemMovimentacao = new DropDownUtil().getNaoSimDropDown("produtoSemMovimentacao"));
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaCentroCusto.setMultiplaSelecao(true);
        autoCompleteConsultaCentroCusto.setOperadorValor(true);
        autoCompleteConsultaLocalizacao.setMultiplaSelecao(true);
        autoCompleteConsultaLocalizacao.setOperadorValor(true);
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioAnaliseEstoqueDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioAnaliseEstoqueDTOParam param) throws ReportException {
        Date data = Data.parserMounthYear(mesAno);
        param.setMesAno(Data.adjustRangeDay(data).getDataInicial());
        param.setAgruparEmpresa("N");
        OperadorValor<List<GrupoProduto>> grupoProdutoVazio = new OperadorValor<List<GrupoProduto>>();
        param.setGruposProduto(grupoProdutoVazio);
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioAnaliseEstoque(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("analiseEstoque");
    }
    
    public DropDown getDropDownCurva(){
        DropDown dropDown = new DropDown("curva");
        dropDown.addChoice(new MapBean( BundleManager.getString("todos"),RepositoryComponentDefault.TODOS) , BundleManager.getString("todos"));
        dropDown.addChoice(new MapBean( BundleManager.getString("curvaA"),Produto.CURVA_A), BundleManager.getString("curvaA"));
        dropDown.addChoice(new MapBean( BundleManager.getString("curvaB"),Produto.CURVA_B) , BundleManager.getString("curvaB"));
        dropDown.addChoice(new MapBean( BundleManager.getString("curvaC"),Produto.CURVA_C), BundleManager.getString("curvaC"));
        
        return dropDown;
    }
    
    public DropDown getDropDownOrdenacao(){
        DropDown dropDown = new DropDown("ordenacao");
        dropDown.addChoice(Produto.PROP_DESCRICAO , BundleManager.getString("descricao"));
        dropDown.addChoice(Produto.PROP_CODIGO , BundleManager.getString("codigo"));
        
        return dropDown;
    }
    
    private DropDown<SubGrupo> getDropDownSubGrupo(){
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
            
        }
        
        return this.dropDownSubGrupo;
    }
    
    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProdutoSubGrupo");
            
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();

                    if (grupoProduto!=null) {
                            List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                    .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                    .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                    .start().getList();

                            if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                                dropDownSubGrupo.removeAllChoices();
                                dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                                for (SubGrupo subGrupo : subGrupos) {
                                    dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                                }
                            }
                            param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });
            
                List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                        .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                        .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                        .start().getList();

                dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));
                
                if (CollectionUtils.isNotNullEmpty(grupos)) {
                    for (GrupoProduto grupoProduto : grupos) {
                        dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                    }
                }
        }
        
        return this.dropDownGrupoProduto;
    }

}
