<wicket:panel>
    <fieldset>
        <div class="field">
            <div class="span-horizontal">
                <fieldset>
                    <h2><label><wicket:message key="validade"/></label></h2>
                    <div class="field">
                        <label>
                            <wicket:message key="utilizarDataFixaValidaAlvara"/>
                        </label>
                        <select wicket:id="configuracaoVigilancia.validadeLicencaDataFixa"/>
                    </div>
                    <div class="field" wicket:id="containerDataBase">
                        <label>
                            <wicket:message key="dataBase"/>
                        </label>
                        <select wicket:id="configuracaoVigilancia.tipoDataCalcLicencaTransporte"/>
                    </div>
                    <div class="field" wicket:id="containerDataVencimentoAtividadeEstabelecimento">
                        <label>
                            <wicket:message key="dataVencimentoAtividadeEstabelecimento"/>
                        </label>
                        <select wicket:id="configuracaoVigilancia.licencaTransporteUsaDataVencimentoDaAtividadeEstabelecimento"/>
                    </div>

                    <div class="field" wicket:id="containerDataVencimento" >
                        <label>
                            <wicket:message key="dataVencimentoValidadeLicenca"/>
                        </label>
                        <input type="text" wicket:id="configuracaoVigilancia.validadeLicencaDataVencimento"/> Dia/Mês
                    </div>

                    <div class="field" wicket:id="containerPeriodoValidadeLicenca" >
                        <label>
                            <wicket:message key="periodoValidadeLicenca"/>
                        </label>
                        <select wicket:id="configuracaoVigilancia.validadeLicencaPeriodo"/>
                    </div>

                    <div class="field" wicket:id="containerLicencaTransporteAnosValidade">
                        <label>
                            <wicket:message key="somarPeriodoValidade"/>
                        </label>
                        <select wicket:id="configuracaoVigilancia.licencaTransporteAnosValidade"/>
                    </div>

                    <div class="field" wicket:id="containerPeriodoSolicitacaoLicenca" >
                        <label>
                            <wicket:message key="periodo"/>
                        </label>
                        <input type="text" wicket:id="configuracaoVigilancia.dataInicialLicencaTransporte"/>
                        <label style="width: 7px;">
                            <wicket:message key="aa"/>
                        </label>
                        <input type="text" wicket:id="configuracaoVigilancia.dataFinalLicencaTransporte"/> Dia/Mês
                    </div>
                </fieldset>

                <fieldset>
                    <h2><label><wicket:message key="geral"/></label></h2>
                    <div class="field no-line"><label>
                        <wicket:message key="numeracaoAnoAnterior"/>
                    </label>
                        <input class="number"
                               maxlength="20"
                               size="20" type="text" wicket:id="configuracaoVigilancia.iniciarProtocoloLicencaTransporteAnoAnterior"/>
                    </div>
                    <div class="field no-line">
                        <label>
                            <wicket:message key="iniciarLicencaEm"/>
                        </label>
                        <input class="number" maxlength="20"
                               size="20" type="text" wicket:id="configuracaoVigilancia.iniciarProtocoloLicencaTransporte"/>
                        <label>
                            <wicket:message key="anoBase"/>
                        </label>
                        <input class="number" maxlength="4" size="6"
                               type="text" wicket:id="configuracaoVigilancia.anoBaseLicencaTransporte"/>
                    </div>
                    <div class="field">
                        <label>
                            <wicket:message key="concedido"/>
                        </label>
                        <input maxlength="200" size="60" type="text" wicket:id="configuracaoVigilancia.concedido"/>
                    </div>
                    <div class="field">
                        <label>
                            <wicket:message key="parametroRefrigeracao"/>
                        </label>
                        <select wicket:id="configuracaoVigilancia.obrigatoridadeRefrigerado"/>
                    </div>
                    <div class="field">
                        <label>
                            <wicket:message key="obrigatorioInformarDadosVeiculo"/>
                        </label>
                        <select wicket:id="configuracaoVigilancia.flagObrigaDadosVeiculo"/>
                    </div>
                </fieldset>
            </div>
        </div>
    </fieldset>
</wicket:panel>