package br.com.celk.view.agenda.agendamento.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.agenda.tipoprocedimentoclassificacao.autocomplete.AutoCompleteConsultaTipoProcedimentoClassificacao;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.prontuario.procedimento.autocomplete.AutoCompleteConsultaProcedimento;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.dto.RelatorioResumoFilaEsperaDTOParam;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioMonitoramentoSolicitacoesDTOParam;
import br.com.ksisolucoes.report.agendamento.interfaces.facade.AgendamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;

@Private
public class RelatorioMonitoramentoSolicitacoesPage extends RelatorioPage<RelatorioMonitoramentoSolicitacoesDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresaOrigem;
    private AutoCompleteConsultaTipoProcedimento autoCompleteConsultaTipoProcedimento;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresa")
                .setValidarTipoEstabelecimento(true)
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        boolean isActionPermittedEmpresa = isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isActionPermittedEmpresa);

        form.add(autoCompleteConsultaEmpresaOrigem = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresaOrigem")
                .setValidarTipoEstabelecimento(true)
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        boolean isActionPermittedEmpresaOrigem = isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EDITAR);
        autoCompleteConsultaEmpresaOrigem.setValidaUsuarioEmpresa(!isActionPermittedEmpresaOrigem);

        form.add(autoCompleteConsultaTipoProcedimento = new AutoCompleteConsultaTipoProcedimento("tipoProcedimento"));
        form.add(new AutoCompleteConsultaProcedimento("procedimento"));

        autoCompleteConsultaTipoProcedimento.setOperadorValor(true);
        autoCompleteConsultaTipoProcedimento.setMultiplaSelecao(true);
        autoCompleteConsultaTipoProcedimento.setIncluirInativos(true);
        form.add(new AutoCompleteConsultaTipoProcedimentoClassificacao("tipoProcedimentoClassificacao")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioMonitoramentoSolicitacoesDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getTipoRelatorioPdfXlsDropDown("tipoArquivo"));
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioMonitoramentoSolicitacoesDTOParam.class;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    @Override
    public DataReport getDataReport(RelatorioMonitoramentoSolicitacoesDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(AgendamentoReportFacade.class).relatorioMonitoramentoSolicitacoes(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioMonitoramentoSolicitacoes");
    }
}