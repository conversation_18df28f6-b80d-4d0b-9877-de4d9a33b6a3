package br.com.celk.view.vigilancia.escalaplantao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.duracaofield.RequiredHoraMinutoField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.frota.veiculo.ConsultaVeiculoPage;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.EscalaPlantao;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;


/**
 * <AUTHOR> S. Schmoeller
 */
@Private

public class CadastroEscalaPlantaoPage extends CadastroPage<EscalaPlantao> {

    private Profissional profissional;
    private EscalaPlantao escalaPlantao;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;

    private RequiredDateChooser dataInicio;
    private RequiredDateChooser dataFim;
    private RequiredHoraMinutoField hmfHoraInicio;
    private RequiredHoraMinutoField hmfHoraFim;

    public CadastroEscalaPlantaoPage(EscalaPlantao object, boolean viewOnly, boolean editar) {
        this(object, viewOnly);
    }

    public CadastroEscalaPlantaoPage(EscalaPlantao object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroEscalaPlantaoPage(EscalaPlantao escalaPlantao) {
        this.escalaPlantao = escalaPlantao;
    }

    public CadastroEscalaPlantaoPage() {
        this(null);
    }

    @Override
    public void init(Form<EscalaPlantao> form) {
        EscalaPlantao proxy = on(EscalaPlantao.class);

        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional(path(proxy.getProfissional()), true));
        form.add(dataInicio = new RequiredDateChooser(path(proxy.getDataInicial())));
        dataInicio.setRequired(true);
        form.add(dataFim = new RequiredDateChooser(path(proxy.getDataFinal())));
        dataFim.setRequired(true);
        form.add(hmfHoraInicio = new RequiredHoraMinutoField(path(proxy.getHoraInicial())));
        form.add(hmfHoraFim = new RequiredHoraMinutoField(path(proxy.getHoraFinal())));

        add(form);
    }

    @Override
    public Class getResponsePage() {
        return ConsultaEscalaPlantaoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroEscalaPlantao");
    }

    @Override
    public Class<EscalaPlantao> getReferenceClass() {
        return EscalaPlantao.class;
    }
}
