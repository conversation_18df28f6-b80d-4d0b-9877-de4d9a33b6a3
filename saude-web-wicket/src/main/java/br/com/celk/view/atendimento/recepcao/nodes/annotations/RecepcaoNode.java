package br.com.celk.view.atendimento.recepcao.nodes.annotations;

import br.com.celk.atendimento.recepcao.NodesRecepcaoRef;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
public @interface RecepcaoNode {
    NodesRecepcaoRef value(); 
}
