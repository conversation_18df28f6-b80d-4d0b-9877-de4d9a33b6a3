package br.com.celk.view.vigilancia.requerimentos;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputarea.RequiredInputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.component.vigilanciaendereco.PnlVigilanciaEndereco;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.util.ComponentWicketUtil;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.vigilancia.RequerimentoVigilanciaFiscaisPanel;
import br.com.celk.view.vigilancia.denuncia.autocomplete.AutoCompleteConsultaTipoDenuncia;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.helper.VigilanciaPageHelper;
import br.com.celk.view.vigilancia.pessoa.DlgCadastroVigilanciaPessoa;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlDadosComumRequerimentoVigilancia;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.AutosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.DadosComumRequerimentoVigilanciaDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PnlRequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoDenunciaReclamacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia;
import br.com.ksisolucoes.vo.vigilancia.denuncia.TipoDenuncia;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import ch.lambdaj.Lambda;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.panel.EmptyPanel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.hamcrest.Matchers;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

@Private
public class RequerimentoDenunciaReclamacaoPage extends BasePage {

    private Form form;
    private RequerimentoDenunciaReclamacaoDTO requerimentoDenunciaReclamacaoDTO;

    private Denuncia denuncia;
    private TipoSolicitacao tipoSolicitacao;
    private RequerimentoVigilancia requerimentoVigilancia;

    private ConfiguracaoVigilancia configuracaoVigilancia;

    private Class classReturn;
    private boolean enabled;

    private AutoCompleteConsultaTipoDenuncia autoCompleteConsultaTipoDenuncia;

    private WebMarkupContainer containerMensagemInformativa;
    private MultiLineLabel lblMensagem;

    private DropDown<Long> ddTipoSigilo;
    private String mensagemDenunciaAnonima;
    private WebMarkupContainer containerJustificativa;
    private InputArea justificativaAnonimo;

    private WebMarkupContainer containerMensagemJustificativa;
    private MultiLineLabel lblMensagemJustificativa;

    private WebMarkupContainer containerDenunciante;
    private DropDown<String> cbxTipoPessoaDenunciante;
    private InputField txtCnpjCpf;
    private InputField denunciante;
    private InputField telefoneDenunciante;
    private InputField emailDenunciante;
    private InputField numeroLogradouroDenunciante;
    private InputField complemento;
    private PnlVigilanciaEndereco pnlEnderecoDenunciante;
    private DropDown<Long> cbxTipoDenunciado;
    private InputField txtTelefoneDenunciado;
    private WebMarkupContainer containerDenunciado;
    private WebMarkupContainer containerPessoaDenunciada;
    private WebMarkupContainer containerPessoaDenunciadaExterno;
    private AutoCompleteConsultaVigilanciaPessoa autoCompleteConsultaVigilanciaPessoa;
    private DlgCadastroVigilanciaPessoa dlgCadastroVigilanciaPessoa;
    private WebMarkupContainer containerEstabelecimentoDenunciado;
    private WebMarkupContainer containerEstabelecimentoDenunciadoExterno;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;
    private WebMarkupContainer containerEnderecoDenunciado;
    private PnlVigilanciaEndereco pnlVigilanciaEnderecoDenunciado;
    private InputField txtNumeroLogradouroDenunciado;
    private PnlDadosComumRequerimentoVigilancia pnlDadosComumRequerimentoVigilancia;
    private RequerimentoVigilanciaFiscaisPanel requerimentoVigilanciaFiscaisPanel;
    private WebMarkupContainer containerEnderecoDenunciadoExterno;
    private WebMarkupContainer containerEnderecoDenuncianteExterno;

    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList;
    private PnlRequerimentoVigilanciaAnexo pnlRequerimentoVigilanciaAnexo;


    public RequerimentoDenunciaReclamacaoPage(TipoSolicitacao tipoSolicitacao, Class clazz) {
        this.tipoSolicitacao = tipoSolicitacao;
        this.classReturn = clazz;

        init(true);
    }

    public RequerimentoDenunciaReclamacaoPage(RequerimentoVigilancia requerimentoVigilancia, Class clazz) {
        this(requerimentoVigilancia, false, clazz);
    }

    public RequerimentoDenunciaReclamacaoPage(RequerimentoVigilancia requerimentoVigilancia, boolean edicao, Class clazz) {
        this.classReturn = clazz;
        this.requerimentoVigilancia = requerimentoVigilancia;

        carregarRequerimentoDenunciaReclamacao(requerimentoVigilancia);

        if (!RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(requerimentoVigilancia.getSituacao()) || !RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(requerimentoVigilancia.getSituacao())) {
            init(edicao);
        } else {
            init(false);
        }
    }

    private void init(boolean edicao) {
        this.enabled = edicao;

        try {
            this.configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        } catch (ValidacaoException e) {
            Loggable.log.error(e.getMessage(), e);
        }

        if (this.requerimentoVigilancia != null) {
            info(VigilanciaHelper.mensagemSituacaoDataAlteracaoRequerimento(requerimentoVigilancia));
        }

        instanceForm();

        RequerimentoDenunciaReclamacaoDTO proxy = on(RequerimentoDenunciaReclamacaoDTO.class);

        form.add(new DisabledInputField<String>(path(proxy.getDenuncia().getRequerimentoVigilancia().getProtocoloFormatado())));

        form.add(autoCompleteConsultaTipoDenuncia = new AutoCompleteConsultaTipoDenuncia(path(proxy.getDenuncia().getTipoDenuncia()), true));
        autoCompleteConsultaTipoDenuncia.setLabel(new Model(bundle("tipoDenuncia")));
        autoCompleteConsultaTipoDenuncia.addAjaxUpdateValue();

        autoCompleteConsultaTipoDenuncia.add(new ConsultaListener<TipoDenuncia>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, TipoDenuncia tipoDenuncia) {
                onEventTipoDenuncia(target, tipoDenuncia, false);
            }
        });

        autoCompleteConsultaTipoDenuncia.add(new RemoveListener<TipoDenuncia>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, TipoDenuncia tipoDenuncia) {
                onEventTipoDenuncia(target, tipoDenuncia, true);
            }
        });

        // DENUNCIANTE
        {

            criarDDTipoSigilo(proxy);
            criarMsgInformativa();
            criarJustificativa(proxy);
            criarMsgJustificativa();
            criarDenunciante(proxy);
        }

        // DENUNCIADO
        {

            containerDenunciado = new WebMarkupContainer("containerDenunciado") {
                @Override
                public boolean isEnabled() {
                    return isEnableContainerDenunciado();
                }
            };
            containerDenunciado.setOutputMarkupPlaceholderTag(true);

            containerDenunciado.add(cbxTipoDenunciado = DropDownUtil.getIEnumDropDown(path(proxy.getDenuncia().getTipoDenunciado()), Denuncia.TipoDenunciado.values()));
            cbxTipoDenunciado.addAjaxUpdateValue();
            cbxTipoDenunciado.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    onEventTipoDenunciado(target);
                }
            });

            containerDenunciado.add(getContainerDenunciadoPessoa(proxy));
            containerDenunciado.add(getContainerDenunciadoEstabelecimento(proxy));

            containerDenunciado.add(txtTelefoneDenunciado = new InputField(path(proxy.getDenuncia().getTelefoneDenunciado())));

            containerDenunciado.add(containerEnderecoDenunciado = new WebMarkupContainer("containerEnderecoDenunciado"));
            containerEnderecoDenunciado.setOutputMarkupPlaceholderTag(true);

            containerEnderecoDenunciado.add(pnlVigilanciaEnderecoDenunciado = new PnlVigilanciaEndereco(path(proxy.getDenuncia().getEnderecoDenunciado())));
            pnlVigilanciaEnderecoDenunciado.getAutoCompleteConsultaVigilanciaEndereco().setLabel(new Model(bundle("enderecoDenunciado")));

            containerEnderecoDenunciado.add(containerEnderecoDenunciadoExterno = new WebMarkupContainer("containerEnderecoDenunciadoExterno"));
            containerEnderecoDenunciadoExterno.setOutputMarkupId(true);

            containerEnderecoDenunciadoExterno.add(new DisabledInputField(path(proxy.getDenuncia().getLogradouroDenunciado())));

            if (denuncia.getLogradouroDenunciado() == null) {
                containerEnderecoDenunciadoExterno.setVisible(false);
            }

            containerEnderecoDenunciado.add(txtNumeroLogradouroDenunciado = new InputField(path(proxy.getDenuncia().getNumeroLogradouroDenunciado())));
            txtNumeroLogradouroDenunciado.setLabel(new Model(bundle("numeroDenunciado")));

            containerEnderecoDenunciado.add(new InputField(path(proxy.getDenuncia().getComplementoLogradouroDenunciado())));
            containerEnderecoDenunciado.add(new InputField(path(proxy.getDenuncia().getPontoReferencia())));

            form.add(containerDenunciado);

            onEventTipoDenunciado(null);
        }


        DadosComumRequerimentoVigilanciaDTOParam dadosComumParam = VigilanciaPageHelper.createDadosComumRequerimentoVigilanciaDTOParam(requerimentoVigilancia);
        form.add(pnlDadosComumRequerimentoVigilancia = new PnlDadosComumRequerimentoVigilancia("dadosComumRequerimentoVigilancia", dadosComumParam, enabled));

        form.add(requerimentoVigilanciaFiscaisPanel = new RequerimentoVigilanciaFiscaisPanel("fiscaisRequerimento", requerimentoVigilancia));

        form.add(new RequerimentoVigilanciaOcorrenciaPanel("ocorrencias", denuncia.getRequerimentoVigilancia().getCodigo(), false).setVisible(!enabled));

        if (requerimentoDenunciaReclamacaoDTO.getDenuncia().getCodigo() == null) {
            try {
                if (configuracaoVigilancia == null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_configuracao"));
                }
                if (configuracaoVigilancia != null) {
                    if (configuracaoVigilancia.getSetorVigilanciaGeralDenuncia() != null) {
                        EloRequerimentoVigilanciaSetorVigilancia elo = new EloRequerimentoVigilanciaSetorVigilancia();
                        elo.setSetorVigilancia(configuracaoVigilancia.getSetorVigilanciaGeralDenuncia());
                        pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().add(elo);
                    }
                }
            } catch (ValidacaoException ex) {
                warn(ex);
            }
        }

        form.add(new RequiredInputArea(path(proxy.getDenuncia().getObservacao())).setLabel(new Model(bundle("motivoDenuncia"))));

        PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
        dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
        dtoPnlAnexo.setTipoSolicitacao(tipoSolicitacao);
        dtoPnlAnexo.setEstabelecimento(denuncia.getEstabelecimentoDenunciado());
        form.add(pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo(dtoPnlAnexo, enabled));
        pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);

        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar();
            }
        }).setEnabled(enabled));

        VoltarButton voltarButton = new VoltarButton("btnVoltar");
        form.add(voltarButton);

        if (!enabled) {
            ComponentUtils.disableComponentsContainer(form);
            voltarButton.setEnabled(true);
        }

        switchTipoSigilo(null);

        add(form);
    }

    private void criarDDTipoSigilo(RequerimentoDenunciaReclamacaoDTO proxy) {
        ddTipoSigilo = DropDownUtil.getIEnumDropDown(path(proxy.getDenuncia().getFlagAnonimo()), Denuncia.TipoSigilo.values(), false, true);
        ddTipoSigilo.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                switchTipoSigilo(ajaxRequestTarget);
            }
        });

        form.add(ddTipoSigilo);
    }

    private void criarMsgInformativa() {
        containerMensagemInformativa = new WebMarkupContainer("containerMensagemInformativa");
        containerMensagemInformativa.setOutputMarkupId(true);

        mensagemDenunciaAnonima = configuracaoVigilancia.getMensagemDenunciaAnonima();

        if (mensagemDenunciaAnonima != null) {
            lblMensagem = new MultiLineLabel("mensagem");
            lblMensagem.setOutputMarkupPlaceholderTag(true);
            lblMensagem.setDefaultModel(new Model(mensagemDenunciaAnonima));
            containerMensagemInformativa.add(lblMensagem);
        } else {
            containerMensagemInformativa.add(new EmptyPanel("mensagem").setOutputMarkupPlaceholderTag(true));
            containerMensagemInformativa.setVisible(false);
        }

        form.add(containerMensagemInformativa);
    }

    private void criarJustificativa(RequerimentoDenunciaReclamacaoDTO proxy) {
        containerJustificativa = new WebMarkupContainer("containerJustificativa");
        containerJustificativa.setOutputMarkupPlaceholderTag(true);

        justificativaAnonimo = new InputArea(path(proxy.getDenuncia().getJustificativaAnonimo()));
        justificativaAnonimo.setOutputMarkupPlaceholderTag(true);
        justificativaAnonimo.setLabel(Model.of(bundle("justificativa")));

        containerJustificativa.add(justificativaAnonimo);
        containerJustificativa.setEnabled(false);

        form.add(containerJustificativa);
    }

    private void criarMsgJustificativa() {
        containerMensagemJustificativa = new WebMarkupContainer("containerMensagemJustificativa");
        containerMensagemJustificativa.setOutputMarkupPlaceholderTag(true);
        String msgJustificativa = bundle("msgAnonimoJustificativa");
        lblMensagemJustificativa = new MultiLineLabel("msgJustificativa");
        lblMensagemJustificativa.setDefaultModel(new Model(msgJustificativa));
        lblMensagemJustificativa.setOutputMarkupPlaceholderTag(true);
        containerMensagemJustificativa.add(lblMensagemJustificativa);

        form.add(containerMensagemJustificativa);
    }

    private void criarDenunciante(RequerimentoDenunciaReclamacaoDTO proxy) {
        containerDenunciante = new WebMarkupContainer("containerDenunciante");
        containerDenunciante.setOutputMarkupId(true);
        containerDenunciante.add(getDropDownTipoPessoaDenunciante(path(proxy.getDenuncia().getTipoPessoaDenunciante())));

        txtCnpjCpf = new InputField(path(proxy.getDenuncia().getCnpjCpfDenunciante()));
        txtCnpjCpf.setLabel(new Model(bundle("cpfCnpjDenunciante")));

        boolean isPessoaJuridica = Pessoa.PESSOA_JURIDICA.equalsIgnoreCase(cbxTipoPessoaDenunciante.getComponentValue());

        ComponentWicketUtil.onEventTipoPessoa(isPessoaJuridica, txtCnpjCpf, null);

        denunciante = new InputField(path(proxy.getDenuncia().getDenunciante()));
        denunciante.setLabel(new Model(bundle("denunciante")));

        telefoneDenunciante = new InputField(path(proxy.getDenuncia().getTelefoneDenunciante()));
        telefoneDenunciante.setLabel(new Model(bundle("telefoneDenunciante")));

        emailDenunciante = new InputField(path(proxy.getDenuncia().getEmailDenunciante()));
        emailDenunciante.setLabel(new Model(bundle("emailDenunciante")));

        numeroLogradouroDenunciante = new InputField(path(proxy.getDenuncia().getNumeroLogradouroDenunciante()));
        numeroLogradouroDenunciante.setLabel(new Model(bundle("numeroDenunciante")));

        complemento = new InputField(path(proxy.getDenuncia().getComplementoLogradouroDenunciante()));

        pnlEnderecoDenunciante = new PnlVigilanciaEndereco(path(proxy.getDenuncia().getEnderecoDenunciante()));
        pnlEnderecoDenunciante.getAutoCompleteConsultaVigilanciaEndereco().setLabel(new Model(bundle("enderecoDenunciante")));

        containerEnderecoDenuncianteExterno = new WebMarkupContainer("containerEnderecoDenuncianteExterno");
        containerEnderecoDenuncianteExterno.setOutputMarkupId(true);
        containerEnderecoDenuncianteExterno.add(new DisabledInputField(path(proxy.getDenuncia().getLogradouroDenunciante())));

        containerDenunciante.add(txtCnpjCpf, denunciante, telefoneDenunciante, emailDenunciante, numeroLogradouroDenunciante, complemento, pnlEnderecoDenunciante, containerEnderecoDenuncianteExterno);

        if (denuncia.getLogradouroDenunciante() == null) {
            containerEnderecoDenuncianteExterno.setVisible(false);
        }

        form.add(containerDenunciante);
    }

    private void switchTipoSigilo(AjaxRequestTarget target) {
        ComponentWicketUtil.addOrRemoveRequired(true, target, listaCamposObrigatorios());
        ComponentWicketUtil.addOrRemoveRequired(false, justificativaAnonimo);
        containerDenunciante.setEnabled(true);
        containerJustificativa.setVisible(false);
        containerJustificativa.setEnabled(false);
        containerMensagemJustificativa.setVisible(false);

        if (isAnonimo()) {
            ComponentWicketUtil.addOrRemoveRequired(false, target, listaCamposObrigatorios());
            ComponentWicketUtil.addOrRemoveRequired(false, target, pnlEnderecoDenunciante.getAutoCompleteConsultaVigilanciaEndereco());
            ComponentWicketUtil.addOrRemoveRequired(true, justificativaAnonimo);
            containerDenunciante.setEnabled(false);
            containerJustificativa.setEnabled(true);
            containerJustificativa.setVisible(true);
            containerMensagemJustificativa.setVisible(true);

            limparCpfCnpj();
        }

        if (target != null) {
            limparJustificativa(target);
            limparDenunciante(target);
            target.add(containerDenunciante, containerJustificativa, justificativaAnonimo, containerMensagemJustificativa, pnlEnderecoDenunciante);
        }
    }

    private void limparJustificativa(AjaxRequestTarget target) {
        justificativaAnonimo.limpar(target);
    }

    private void limparDenunciante(AjaxRequestTarget target) {
        ComponentWicketUtil.limpar(target, listaCamposObrigatorios());
    }

    private void limparCpfCnpj() {
        if (requerimentoVigilancia != null && requerimentoVigilancia.getCodigo() != null) {
            requerimentoVigilancia.setCpfSolicitante(null);
            requerimentoVigilancia.setRgCpfSolicitante(null);
        }
        if (denuncia != null && denuncia.getCodigo() != null) {
            denuncia.setCnpjCpfDenunciante(null);
        }
    }

    private List<FormComponent> listaCamposObrigatorios() {
        return Arrays.asList(txtCnpjCpf, denunciante, telefoneDenunciante, emailDenunciante, numeroLogradouroDenunciante);
    }

    public boolean isSigiloso() {
        return denuncia.getFlagAnonimo() != null && Denuncia.TipoSigilo.SIGILOSO.value().equals(denuncia.getFlagAnonimo());
    }

    public boolean isAnonimo() {
        return denuncia.getFlagAnonimo() != null && Denuncia.TipoSigilo.ANONIMO.value().equals(denuncia.getFlagAnonimo());
    }

    public boolean isSemSigilo() {
        return denuncia.getFlagAnonimo() != null && Denuncia.TipoSigilo.SEM_SIGILO.value().equals(denuncia.getFlagAnonimo());
    }

    private boolean isEnableContainerDenunciado() {
        if (denuncia.getCodigo() != null) {
            return !AutosHelper.hasAutoInfracaoDenuncia(requerimentoVigilancia)
                    && !AutosHelper.hasAutoIntimacaoDenuncia(requerimentoVigilancia)
                    && !AutosHelper.hasAutoMultaDenuncia(requerimentoVigilancia)
                    && !AutosHelper.hasAutoPenalidadeDenuncia(requerimentoVigilancia)
                    && !RequerimentoVigilancia.Situacao.DEFERIDO.value().equals(requerimentoVigilancia.getSituacao()) || !RequerimentoVigilancia.Situacao.INDEFERIDO.value().equals(requerimentoVigilancia.getSituacao());
        } else {
            return true;
        }
    }

    private void instanceForm() {
        if (form == null) {

            if (requerimentoDenunciaReclamacaoDTO == null) {
                requerimentoDenunciaReclamacaoDTO = new RequerimentoDenunciaReclamacaoDTO();

                if (denuncia == null) {
                    denuncia = new Denuncia();
                    denuncia.setRequerimentoVigilancia(new RequerimentoVigilancia());
                    denuncia.setFlagAnonimo(RepositoryComponentDefault.SIM_LONG);
                }

                requerimentoDenunciaReclamacaoDTO.setDenuncia(denuncia);
            }

            form = new Form("form", new CompoundPropertyModel(requerimentoDenunciaReclamacaoDTO));
        }
    }

    public DropDown getDropDownTipoPessoaDenunciante(String id) {
        cbxTipoPessoaDenunciante = new DropDown(id);

        cbxTipoPessoaDenunciante.addChoice(Pessoa.PESSOA_FISICA, BundleManager.getString("fisica"));
        cbxTipoPessoaDenunciante.addChoice(Pessoa.PESSOA_JURIDICA, BundleManager.getString("juridica"));

        cbxTipoPessoaDenunciante.addAjaxUpdateValue();
        cbxTipoPessoaDenunciante.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean isPessoaJuridica = Pessoa.PESSOA_JURIDICA.equalsIgnoreCase(cbxTipoPessoaDenunciante.getComponentValue());
                ComponentWicketUtil.onEventTipoPessoa(isPessoaJuridica, txtCnpjCpf, target);
            }
        });

        return cbxTipoPessoaDenunciante;
    }

    private WebMarkupContainer getContainerDenunciadoEstabelecimento(RequerimentoDenunciaReclamacaoDTO proxy) {
        containerEstabelecimentoDenunciado = new WebMarkupContainer("containerEstabelecimentoDenunciado");
        containerEstabelecimentoDenunciado.setOutputMarkupPlaceholderTag(true);

        containerEstabelecimentoDenunciado.add(autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEstabelecimento(path(proxy.getDenuncia().getEstabelecimentoDenunciado())));
        autoCompleteConsultaEstabelecimento.setLabel(new Model(bundle("estabelecimentoDenunciado")));
        autoCompleteConsultaEstabelecimento.add(new ConsultaListener<Estabelecimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Estabelecimento object) {
                onEventEstabelecimentoDenunciado(target, object);
            }
        });

        autoCompleteConsultaEstabelecimento.add(new RemoveListener<Estabelecimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Estabelecimento object) {
                onEventEstabelecimentoDenunciado(target, null);
                pnlRequerimentoVigilanciaAnexo.limpar(target);
            }
        });


        containerEstabelecimentoDenunciado.add(containerEstabelecimentoDenunciadoExterno = new WebMarkupContainer("containerEstabelecimentoDenunciadoExterno"));
        containerEstabelecimentoDenunciadoExterno.setOutputMarkupId(true);

        containerEstabelecimentoDenunciadoExterno.add(new DisabledInputField(path(proxy.getDenuncia().getDenunciado())));

        if (denuncia.getDenunciado() == null) {
            containerEstabelecimentoDenunciadoExterno.setVisible(false);
        }

        return containerEstabelecimentoDenunciado;
    }

    private WebMarkupContainer getContainerDenunciadoPessoa(RequerimentoDenunciaReclamacaoDTO proxy) {
        containerPessoaDenunciada = new WebMarkupContainer("containerPessoaDenunciada");
        containerPessoaDenunciada.setOutputMarkupPlaceholderTag(true);

        containerPessoaDenunciada.add(autoCompleteConsultaVigilanciaPessoa = new AutoCompleteConsultaVigilanciaPessoa(path(proxy.getDenuncia().getVigilanciaPessoaDenunciada())));
        autoCompleteConsultaVigilanciaPessoa.setLabel(new Model(bundle("pessoaDenunciada")));
        autoCompleteConsultaVigilanciaPessoa.add(new ConsultaListener<VigilanciaPessoa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, VigilanciaPessoa object) {
                onEventPessoaDenunciada(target, object);
            }
        });

        autoCompleteConsultaVigilanciaPessoa.add(new RemoveListener<VigilanciaPessoa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, VigilanciaPessoa object) {
                onEventPessoaDenunciada(target, null);
            }
        });

        containerPessoaDenunciada.add(new AbstractAjaxLink("btnCadPessoa") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                if (dlgCadastroVigilanciaPessoa == null) {
                    addModal(target, dlgCadastroVigilanciaPessoa = new DlgCadastroVigilanciaPessoa(newModalId(), false, false) {
                        @Override
                        public void setVigilanciaPessoa(AjaxRequestTarget target, VigilanciaPessoa vigilanciaPessoa) {
                            autoCompleteConsultaVigilanciaPessoa.limpar(target);
                            autoCompleteConsultaVigilanciaPessoa.setComponentValue(target, vigilanciaPessoa);

                            onEventPessoaDenunciada(target, vigilanciaPessoa);
                        }
                    });
                }

                dlgCadastroVigilanciaPessoa.show(target, new VigilanciaPessoa());
            }
        });

        containerPessoaDenunciada.add(containerPessoaDenunciadaExterno = new WebMarkupContainer("containerPessoaDenunciadaExterno"));
        containerPessoaDenunciadaExterno.setOutputMarkupId(true);

        containerPessoaDenunciadaExterno.add(new DisabledInputField(path(proxy.getDenuncia().getDenunciado())));

        if (denuncia.getDenunciado() == null) {
            containerPessoaDenunciadaExterno.setVisible(false);
        }

        return containerPessoaDenunciada;
    }

    private void onEventTipoDenuncia(AjaxRequestTarget target, TipoDenuncia tipoDenuncia, boolean remover) {
        TipoDenuncia tipoDenunciaLoad = LoadManager.getInstance(TipoDenuncia.class)
                .addProperties(new HQLProperties(SetorVigilancia.class, TipoDenuncia.PROP_SETOR_VIGILANCIA).getProperties())
                .setId(tipoDenuncia.getCodigo())
                .start().getVO();

        if (tipoDenunciaLoad.getSetorVigilancia() != null) {

            EloRequerimentoVigilanciaSetorVigilancia eloRequerimentoVigilanciaSetorVigilancia = Lambda.selectUnique(
                    pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList(),
                    Lambda.having(Lambda.on(EloRequerimentoVigilanciaSetorVigilancia.class).getSetorVigilancia(), Matchers.equalTo(tipoDenunciaLoad.getSetorVigilancia()))
            );

            if (remover) {
                pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().remove(eloRequerimentoVigilanciaSetorVigilancia);

                if (eloRequerimentoVigilanciaSetorVigilancia.getCodigo() != null) {
                    pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaExcluirList().add(eloRequerimentoVigilanciaSetorVigilancia);
                }
            } else if (eloRequerimentoVigilanciaSetorVigilancia == null) {
                eloRequerimentoVigilanciaSetorVigilancia = new EloRequerimentoVigilanciaSetorVigilancia();
                eloRequerimentoVigilanciaSetorVigilancia.setSetorVigilancia(tipoDenunciaLoad.getSetorVigilancia());

                pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList().add(eloRequerimentoVigilanciaSetorVigilancia);
            }

        }

        pnlDadosComumRequerimentoVigilancia.getTblSetorResponsavel().update(target);
    }

    private void onEventTipoDenunciado(AjaxRequestTarget target) {
        boolean isEstabelecimento = Denuncia.TipoDenunciado.ESTABELECIMENTO.value().equals(cbxTipoDenunciado.getComponentValue());

        if (isEstabelecimento) {
            containerEstabelecimentoDenunciado.setVisible(true);
            containerPessoaDenunciada.setVisible(false);
        } else {
            containerPessoaDenunciada.setVisible(true);
            containerEstabelecimentoDenunciado.setVisible(false);
        }

        if (target != null) {
            ComponentUtils.limparContainer(containerPessoaDenunciada, target);
            ComponentUtils.limparContainer(containerEstabelecimentoDenunciado, target);

            denuncia.setEnderecoDenunciado(null);
            pnlVigilanciaEnderecoDenunciado.limpar(target);

            // ComponentUtils.limparContainer(containerEnderecoDenunciado, target);

            target.add(containerEnderecoDenunciado);

            target.appendJavaScript(JScript.initMasks());
        }
    }

    public void onEventEstabelecimentoDenunciado(AjaxRequestTarget target, Estabelecimento estabelecimento) {
        denuncia.setEnderecoDenunciado(null);
        pnlVigilanciaEnderecoDenunciado.limpar(target);

        // ComponentUtils.limparContainer(containerEnderecoDenunciado, target);

        if (estabelecimento != null) {
            VigilanciaEndereco ve = VigilanciaHelper.carregarVigilanciaEnderecoEstabelecimento(estabelecimento);
            denuncia.setEnderecoDenunciado(ve);

            if (estabelecimento.getTelefone() != null) {
                denuncia.setTelefoneDenunciado(estabelecimento.getTelefone());
            } else if (estabelecimento.getCelular() != null) {
                denuncia.setTelefoneDenunciado(estabelecimento.getCelular());
            }

            pnlRequerimentoVigilanciaAnexo.filtrarAtividadeEstabelecimento(target,estabelecimento);
        }

        target.add(txtTelefoneDenunciado);
        target.add(containerEnderecoDenunciado);
        target.appendJavaScript(JScript.initMasks());
    }

    private void onEventPessoaDenunciada(AjaxRequestTarget target, VigilanciaPessoa vigilanciaPessoa) {
        denuncia.setEnderecoDenunciado(null);
        pnlVigilanciaEnderecoDenunciado.limpar(target);

        // ComponentUtils.limparContainer(containerEnderecoDenunciado, target);

        if (vigilanciaPessoa != null) {
            VigilanciaPessoa vp = LoadManager.getInstance(VigilanciaPessoa.class)
                    .setId(vigilanciaPessoa.getCodigo())
                    .start().getVO();

            if (vp.getCelular() != null) {
                denuncia.setTelefoneDenunciado(vp.getCelular());
            }

            VigilanciaEndereco ve = VigilanciaHelper.carregarVigilanciaEnderecoPessoa(vigilanciaPessoa);
            denuncia.setEnderecoDenunciado(ve);

            if (vp.getNumeroLogradouro() != null) {
                denuncia.setNumeroLogradouroDenunciado(String.valueOf(vp.getNumeroLogradouro()));
            }
        }

        target.add(txtTelefoneDenunciado);
        target.add(containerEnderecoDenunciado);
        target.appendJavaScript(JScript.initMasks());
    }

    private void carregarRequerimentoDenunciaReclamacao(RequerimentoVigilancia requerimentoVigilancia) {
        if (requerimentoVigilancia != null) {
            tipoSolicitacao = requerimentoVigilancia.getTipoSolicitacao();

            carregarAnexos(requerimentoVigilancia);

            denuncia = LoadManager.getInstance(Denuncia.class)
                    .addProperties(new HQLProperties(Denuncia.class).getProperties())
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class, Denuncia.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                    .addProperties(new HQLProperties(VigilanciaPessoa.class, VOUtils.montarPath(Denuncia.PROP_VIGILANCIA_PESSOA_DENUNCIADA)).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(Denuncia.PROP_ESTABELECIMENTO_DENUNCIADO)).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(Denuncia.PROP_ENDERECO_DENUNCIADO)).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(Denuncia.PROP_ENDERECO_DENUNCIANTE)).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(Denuncia.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_ESTABELECIMENTO)).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(Denuncia.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_ESTABELECIMENTO, Estabelecimento.PROP_ESTABELECIMENTO_PRINCIPAL)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Denuncia.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_TIPO_DOCUMENTO), TipoSolicitacao.TipoDocumento.DENUNCIA_RECLAMACAO.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(Denuncia.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .start().getVO();

            if (denuncia != null) {

                if (denuncia.getRequerimentoVigilancia().getEstabelecimento() != null) {
                    if (denuncia.getRequerimentoVigilancia().getEstabelecimento().getEstabelecimentoPrincipal() != null
                            && (denuncia.getRequerimentoVigilancia().getEstabelecimento().getCnpjCpf() == null
                            || denuncia.getRequerimentoVigilancia().getEstabelecimento().getCnpjCpf().isEmpty())) {

                        Estabelecimento estabelecimentoPrincipal = LoadManager.getInstance(Estabelecimento.class)
                                .addParameter(new QueryCustom.QueryCustomParameter(Estabelecimento.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, denuncia.getRequerimentoVigilancia().getEstabelecimento().getEstabelecimentoPrincipal().getCodigo()))
                                .start().getVO();

                        denuncia.getRequerimentoVigilancia().getEstabelecimento().setCnpjCpf(estabelecimentoPrincipal.getCnpjCpfFormatado());
                    }

                    EstabelecimentoAtividade estabelecimentoAtividade = getEstabelecimentoAtividade(denuncia.getRequerimentoVigilancia().getEstabelecimento());
                    if (estabelecimentoAtividade != null && estabelecimentoAtividade.getAtividadeEstabelecimento() != null) {
                        denuncia.getRequerimentoVigilancia().getEstabelecimento().setAtividadeEstabelecimento(estabelecimentoAtividade.getAtividadeEstabelecimento());
                    }

                }

            }

        }
    }

    public void setEstabelecimentoDenunciado(Estabelecimento estabelecimento) {
        denuncia.setEstabelecimentoDenunciado(estabelecimento);
    }

    private EstabelecimentoAtividade getEstabelecimentoAtividade(Estabelecimento estabelecimento) {
        return LoadManager.getInstance(EstabelecimentoAtividade.class)
                .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, estabelecimento))
                .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_FLAG_PRINCIPAL, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.SIM_LONG))
                .start().getVO();
    }

    private void carregarAnexos(RequerimentoVigilancia rv) {
        List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(rv);

        requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        RequerimentoVigilanciaAnexoDTO anexo;
        for (RequerimentoVigilanciaAnexo rva : list) {
            anexo = new RequerimentoVigilanciaAnexoDTO();
            anexo.setDescricaoAnexo(rva.getDescricao());
            anexo.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexo.setRequerimentoVigilanciaAnexo(rva);

            requerimentoVigilanciaAnexoDTOList.add(anexo);
        }
    }

    private void salvar() throws ValidacaoException, DAOException {
        atualizarDadosComuns();

        RequerimentoVigilancia requerimentoVigilancia = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoDenunciaReclamacao(requerimentoDenunciaReclamacaoDTO);

        try {

            Page pageReturn = (Page) classReturn.newInstance();
            getSession().getFeedbackMessages().info(pageReturn, BundleManager.getString("registro_salvo_sucesso_protocolo_x", requerimentoVigilancia.getProtocoloFormatado()));
            setResponsePage(pageReturn);

        } catch (InstantiationException | IllegalAccessException e) {
            Loggable.log.error(e.getMessage(), e);
        }
    }

    private void atualizarDadosComuns() {
        denuncia.getRequerimentoVigilancia().setTipoSolicitacao(tipoSolicitacao);
        denuncia.getRequerimentoVigilancia().setObservacaoRequerimento(pnlDadosComumRequerimentoVigilancia.getParam().getObservacaoRequerimento());

        requerimentoDenunciaReclamacaoDTO.setEloRequerimentoVigilanciaSetorVigilanciaList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaList());
        requerimentoDenunciaReclamacaoDTO.setEloRequerimentoVigilanciaSetorVigilanciaExcluirList(pnlDadosComumRequerimentoVigilancia.getParam().getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
        requerimentoDenunciaReclamacaoDTO.setRequerimentoVigilanciaFiscalList(pnlDadosComumRequerimentoVigilancia.getParam().getRequerimentoVigilanciaFiscalList());
        requerimentoDenunciaReclamacaoDTO.setRequerimentoVigilanciaFiscalListExcluir(pnlDadosComumRequerimentoVigilancia.getParam().getRequerimentoVigilanciaFiscalListExcluir());
        requerimentoDenunciaReclamacaoDTO.setRequerimentoVigilanciaAnexoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoDTOList());
        requerimentoDenunciaReclamacaoDTO.setRequerimentoVigilanciaAnexoExcluidoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoExcluidoDTOList());
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroRegistroDenuncia");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);

        if (denuncia.getCnpjCpfDenunciante() != null) {
            if (Pessoa.PESSOA_JURIDICA.equalsIgnoreCase(denuncia.getTipoPessoaDenunciante())) {
                response.render(OnDomReadyHeaderItem.forScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('99.999.999/9999-99');"));
            } else {
                response.render(OnDomReadyHeaderItem.forScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('999.999.999-99');"));
            }
        }
    }
}
