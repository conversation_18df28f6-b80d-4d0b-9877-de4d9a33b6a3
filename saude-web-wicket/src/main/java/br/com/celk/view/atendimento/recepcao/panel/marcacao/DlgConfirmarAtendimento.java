package br.com.celk.view.atendimento.recepcao.panel.marcacao;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.util.ImagemAvatarHelper;
import br.com.celk.system.util.MessageUtil;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ConsultaUsuarioCadsusDTO;
import br.com.ksisolucoes.bo.recepcao.interfaces.dto.ConfirmacaoAtendimentoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import org.apache.wicket.ajax.AjaxRequestTarget;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConfirmarAtendimento extends Window {

    private PnlConfirmarAtendimento pnlConfirmarAtendimento;

    public DlgConfirmarAtendimento(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("confirmarMarcacao"));

        setInitialWidth(800);
        setInitialHeight(370);

        setResizable(false);

        setContent(pnlConfirmarAtendimento = new PnlConfirmarAtendimento(getContentId()) {
            @Override
            public void onOk(AjaxRequestTarget target, ConfirmacaoAtendimentoDTO dto) throws ValidacaoException, DAOException {
                try {
                    validarTipoAtendimento(pnlConfirmarAtendimento.getTipoAtendimento());
                    close(target);
                    DlgConfirmarAtendimento.this.onOk(target, dto);
                } catch (ValidacaoException ex) {
                    MessageUtil.modalWarn(target, this, ex);
                }
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }

    public abstract void onOk(AjaxRequestTarget target, ConfirmacaoAtendimentoDTO dto) throws ValidacaoException, DAOException;

    public void onFechar(AjaxRequestTarget target) {
    }

    public void validarTipoAtendimento(TipoAtendimento ta) throws ValidacaoException {
        if (ta == null) {
            throw new ValidacaoException(bundle("magValidarTipoAtendimento"));
        }
    }

    public void show(AjaxRequestTarget target, ConsultaUsuarioCadsusDTO dto) {
        pnlConfirmarAtendimento.limpar(target);
        pnlConfirmarAtendimento.setPaciente(dto.getUsuarioCadsus().getNomeSocial());
        pnlConfirmarAtendimento.setDto(target, dto);
        pnlConfirmarAtendimento.setResourceImage(target, ImagemAvatarHelper.carregarAvatarParams(dto.getUsuarioCadsus()));

        show(target);
    }

    private void fechar(AjaxRequestTarget target) {
        DlgConfirmarAtendimento.this.onFechar(target);
        close(target);
    }
}
