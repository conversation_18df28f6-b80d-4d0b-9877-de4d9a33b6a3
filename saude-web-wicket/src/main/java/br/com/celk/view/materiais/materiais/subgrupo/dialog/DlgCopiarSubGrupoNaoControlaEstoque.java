package br.com.celk.view.materiais.materiais.subgrupo.dialog;

import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ConsultaUsuarioCadsusDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgCopiarSubGrupoNaoControlaEstoque extends Window{
    
    private PnlCopiarSubGrupoNaoControlaEstoque pnlCopiarSubGrupoNaoControlaEstoque;
    
    public DlgCopiarSubGrupoNaoControlaEstoque(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("copiar");
            }
        });
                
        setInitialWidth(660);
        setInitialHeight(110);
        setResizable(true);
        
        setContent(pnlCopiarSubGrupoNaoControlaEstoque = new PnlCopiarSubGrupoNaoControlaEstoque(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, Empresa empresaOrigem, Empresa empresaDestino) throws ValidacaoException, DAOException {
                close(target);
                DlgCopiarSubGrupoNaoControlaEstoque.this.onConfirmar(target, empresaOrigem, empresaDestino);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, Empresa empresaOrigem, Empresa empresaDestino) throws ValidacaoException, DAOException;
    
    public void showDlg(AjaxRequestTarget target){
        show(target);
        pnlCopiarSubGrupoNaoControlaEstoque.limpar(target);
    }    
}