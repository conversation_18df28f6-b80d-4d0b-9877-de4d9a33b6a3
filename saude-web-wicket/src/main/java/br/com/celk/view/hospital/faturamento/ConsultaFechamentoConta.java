package br.com.celk.view.hospital.faturamento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dialog.DlgMotivoObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.MesAnoField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.usuario.autocomplete.AutoCompleteConsultaUsuario;
import br.com.celk.view.hospital.faturamento.dialogs.DlgDadosGuiaSolicitacao;
import br.com.celk.view.hospital.faturamento.ipe.ConsultaFechamentoContaIpePage;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.CadastroFechamentoContaDTO;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioImpressaoAtendimentoDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import br.com.ksisolucoes.vo.prontuario.procedimento.TipoTabelaProcedimento;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaFechamentoConta extends ConsultaPage<ContaPaciente, List<BuilderQueryCustom.QueryParameter>> {

    private Long atendimento;
    private String paciente;
    private Empresa empresa;
    private Long situacao;
    private String tipoPeriodo;
    private DatePeriod periodo;
    private Date competencia;
    private Usuario usuarioFechamento;
    private Convenio convenio;
    private Convenio convenioIpe;
    private TipoAtendimento tipoAtendimento;
    private Long contaPaciente;

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;

    private DropDown<Long> dropDownSituacao;
    private DropDown<String> dropDownTipoPeriodo;
    private DropDown dropDownConvenio;
    private DropDown dropDownTipoAtendimento;
    private Long codigoRegistroSalvo;
    private DlgMotivoObject<ContaPaciente> dlgMotivo;
    private DlgDadosGuiaSolicitacao dlgDadosGuiaSolicitacao;

    private List<Long> codigosEmpresasUsuario;
    private DlgImpressaoObject<ContaPaciente> dlgConfirmacaoImpressaoProntuario;

    public ConsultaFechamentoConta() {
    }

    public ConsultaFechamentoConta(Long atendimento) {
        this.atendimento = atendimento;

        setProcurarAoAbrir(true);
        getPageableTable().populate();
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        try {
            codigosEmpresasUsuario = BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario((SessaoAplicacaoImp.getInstance().getUsuario()));
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        form.add(new LongField("atendimento"));
        form.add(new InputField("paciente"));
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa"));
        if (!isActionPermitted(Permissions.EMPRESA, ConsultaFechamentoConta.class)) {
            autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(true);
        }
        form.add(getDropDownSituacao());
        form.add(getDropDownTipoPeriodo());
        form.add(new PnlDatePeriod("periodo"));
        form.add(new MesAnoField("competencia"));
        form.add(new AutoCompleteConsultaUsuario("usuarioFechamento"));
        form.add(getDropDownConvenio());
        form.add(getDropDownTipoAtendimento());
        form.add(new InputField("contaPaciente"));

        getPageableTable().setScrollX("1720px");
        setProcurarAoAbrir(false);

        addModal(dlgMotivo = new DlgMotivoObject<ContaPaciente>(newModalId(), BundleManager.getString("motivoReaberturaConta")) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, String motivo, ContaPaciente contaPaciente) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(HospitalFacade.class).reabrirContaPaciente(contaPaciente, motivo);
                ConsultaFechamentoConta consultaFechamentoConta = new ConsultaFechamentoConta();
                consultaFechamentoConta.setCodigoRegistroSalvo(contaPaciente.getCodigo());
                setResponsePage(consultaFechamentoConta);
            }
        });

        boolean isPermissaoNovoAtendimento = isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.REGISTRAR, ConsultaFechamentoConta.class);

        getLinkNovo().add(new AttributeModifier("value", BundleManager.getString("novaConta")));
        if (!permissaoNovaConta()) {
            getLinkNovo().setVisible(false);
        }

        AbstractAjaxButton btnNovoAtendimento;
        getControls().add(btnNovoAtendimento = new AbstractAjaxButton(getControls().newChildId()) {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                PageParameters parametro = new PageParameters();
                parametro.set(CadastroFechamentoContaDTO.PROP_ORIGEM_CONTA, CadastroFechamentoContaDTO.ORIGEM_CONTA_NORMAL);
                setResponsePage(CadastroNovoAtendimentoFechamentoContaPage.class, parametro);
            }
        });

        btnNovoAtendimento.add(new AttributeModifier("class", "doc-new"));
        btnNovoAtendimento.add(new AttributeModifier("value", bundle("novoAtendimento")));
        btnNovoAtendimento.add(new AttributeModifier("style", "margin-left: 5px;"));

        if (!isPermissaoNovoAtendimento) {
            btnNovoAtendimento.setVisible(false);
        }

        setExibeExpandir(true);
    }

    private boolean permissaoNovaConta() {
        return isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.CADASTRAR, ConsultaFechamentoConta.class);
    }

    @Override
    public List getColumns(List columns) {
        ContaPaciente proxy = on(ContaPaciente.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("atendimento"), proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getCodigo()));
        columns.add(createSortableColumn(bundle("conta"), proxy.getCodigo()));
        columns.add(createSortableColumn(bundle("sequencia"), proxy.getSequencia()));
        columns.add(createSortableColumn(bundle("paciente"), proxy.getAtendimentoInformacao().getUsuarioCadsus().getNome(), proxy.getAtendimentoInformacao().getUsuarioCadsus().getNomeSocial()));
        columns.add(createSortableColumn(bundle("idade"), proxy.getAtendimentoInformacao().getUsuarioCadsus().getDataNascimento(), proxy.getAtendimentoInformacao().getDescricaoIdade()));
        columns.add(new DateTimeColumn<AtendimentoInformacao>(bundle("dataChegada"), path(proxy.getAtendimentoInformacao().getDataChegada()), path(proxy.getAtendimentoInformacao().getDataChegada())).setPattern("dd/MM/yyyy HH:mm:ss"));
        columns.add(new DateTimeColumn<AtendimentoInformacao>(bundle("dataAbertura"), path(proxy.getDataAbertura()), path(proxy.getDataAbertura())).setPattern("dd/MM/yyyy HH:mm:ss"));
        columns.add(createSortableColumn(bundle("convenio"), proxy.getConvenio().getDescricao()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getStatus(), proxy.getDescricaoStatus()));
        columns.add(new DateTimeColumn(bundle("competencia"), path(proxy.getCompetenciaAtendimento()), path(proxy.getCompetenciaAtendimento())).setPattern("MM/yyyy"));
        columns.add(createSortableColumn(bundle("tipoAtendimento"), proxy.getAtendimentoInformacao().getTipoAtendimentoFaturamento().getDescricao()));
        columns.add(createSortableColumn(bundle("estabelecimento"), proxy.getAtendimentoInformacao().getEmpresa().getDescricao()));
        columns.add(new DateTimeColumn<AtendimentoInformacao>(bundle("dataSaida"), path(proxy.getAtendimentoInformacao().getDataSaida()), path(proxy.getAtendimentoInformacao().getDataSaida())).setPattern("dd/MM/yyyy HH:mm:ss"));
        columns.add(new DateTimeColumn(bundle("dataFechamento"), path(proxy.getDataFechamento()), path(proxy.getDataFechamento())));
        columns.add(createSortableColumn(bundle("usuarioFechamento"), proxy.getUsuarioFechamento().getNome(), proxy.getUsuarioFechamento().getNome()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<ContaPaciente>() {
            @Override
            public void customizeColumn(ContaPaciente rowObject) {
                boolean aberta = rowObject.getStatus().equals(ContaPaciente.Status.ABERTA.value());
                boolean fechadaCancelada = !aberta;

                addAction(ActionType.MANUTENCAO, rowObject, new IModelAction<ContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ContaPaciente modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new FechamentoContaPage(modelObject));
                    }
                }).setTitleBundleKey("manutencaoDaConta")
                        .setVisible(aberta);

                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<ContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ContaPaciente contaPaciente) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(HospitalFacade.class).fechaContaPaciente(contaPaciente);
                        ConsultaFechamentoConta consultaFechamentoConta = new ConsultaFechamentoConta();
                        consultaFechamentoConta.setCodigoRegistroSalvo(contaPaciente.getCodigo());
                        setResponsePage(consultaFechamentoConta);
                    }
                }).setQuestionDialogBundleKey("msg_deseja_fechar_conta")
                        .setTitleBundleKey("fecharContaDoPaciente")
                        .setVisible(aberta);

                addAction(ActionType.REATIVAR, rowObject, new IModelAction<ContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ContaPaciente modelObject) throws ValidacaoException, DAOException {
                        dlgMotivo.setObject(modelObject);
                        dlgMotivo.show(target);
                    }
                }).setTitleBundleKey("reabrirContaPaciente")
                        .setIcon(Icon.ICON_UNBLOCKED)
                        .setVisible(fechadaCancelada);

                addAction(ActionType.RECALCULAR, rowObject, new IModelAction<ContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ContaPaciente modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(HospitalFacade.class).recalcularPrecosContaPaciente(modelObject.getCodigo(),
                                Arrays.asList(
                                        ItemContaPaciente.Tipo.EXAME.value(),
                                        ItemContaPaciente.Tipo.PROCEDIMENTO.value(),
                                        ItemContaPaciente.Tipo.MATERIAL_MEDICAMENTO.value(),
                                        ItemContaPaciente.Tipo.HONORARIO_TISS.value()));
                    }
                }).setQuestionDialogBundleKey("desejaRecalcularPrecos")
                        .setTitleBundleKey("recalcularPrecos")
                        .setIcon(Icon.CALC)
                        .setVisible(!rowObject.getStatus().equals(ContaPaciente.Status.FECHADA.value())
                                && !rowObject.getStatus().equals(ContaPaciente.Status.CANCELADA.value())
                                && isActionPermitted(getUsuarioLogado(), Permissions.RECALCULAR, ConsultaFechamentoConta.class));

                addAction(ActionType.CANCELAR, rowObject, new IModelAction<ContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ContaPaciente modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(HospitalFacade.class).cancelarContaPaciente(modelObject);
                        getPageableTable().update(target);
                    }
                }).setVisible(aberta);

                addAction(ActionType.OCORRENCIA, rowObject, new IModelAction<ContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ContaPaciente modelObject) throws ValidacaoException, DAOException {
                        viewDlgDadosGuiaSolicitacao(target, modelObject);
                    }
                }).setIcon(Icon.NOTEPAD)
                        .setTitleBundleKey("guiaSolicitacao")
                        .setVisible(aberta);

                addAction(ActionType.CLONAR, rowObject, new IModelAction<ContaPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, ContaPaciente modelObject) throws ValidacaoException, DAOException {
                        desdobrarConta(target, modelObject);
                    }
                }).setIcon(Icon.ROUND_ARROW)
                        .setQuestionDialogBundleKey("msgDesdobrarContaPaciente")
                        .setTitleBundleKey("desdobrarConta")
                        .setVisible(permissaoNovaConta() && !ContaPaciente.Status.FECHADA.value().equals(rowObject.getStatus())
                                && !ContaPaciente.Status.CANCELADA.value().equals(rowObject.getStatus()))
                        .setEnabled(enableDesdobramentoConta(rowObject));

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<ContaPaciente>() {
                    @Override
                    public DataReport action(ContaPaciente modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(HospitalFacade.class).relatorioImpressaoFechamentoContaPaciente(modelObject);
                    }
                }).setTitleBundleKey("imprimirContaPaciente");

                addAction(ActionType.IMPRIMIR_MOD, rowObject, new IReportAction<ContaPaciente>() {
                    @Override
                    public DataReport action(ContaPaciente contaPaciente) throws ReportException {
                        RelatorioImpressaoAtendimentoDTOParam param = new RelatorioImpressaoAtendimentoDTOParam();
                        param.setAtendimento(contaPaciente.getAtendimentoInformacao().getAtendimentoPrincipal());
                        param.setCorrecao(RepositoryComponentDefault.NAO_LONG);
                        param.setCiclo(Coalesce.asLong(contaPaciente.getAtendimentoInformacao().getSequenciaCiclo()));

                        return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoAtendimentoInternacao(param);
                    }
                }).setTitleBundleKey("imprimirProntuarioAtendimento").setVisible(rowObject.getStatus().equals(ContaPaciente.Status.FECHADA.value()));
            }
        };
    }

    private boolean enableDesdobramentoConta(ContaPaciente cp) {
        if (permissaoNovaConta()) {
            int diasInternacao = DataUtil.getDiasDiferenca(
                    Data.adjustRangeHour(cp.getAtendimentoInformacao().getDataChegada()).getDataInicial(),
                    Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial());

            if (new Dinheiro(diasInternacao).dividir(30D).longValue() >= new Dinheiro(Coalesce.asLong(cp.getAtendimentoInformacao().getSequenciaCiclo())).somar(1D).longValue()) {
                return true;
            }
        }
        return false;
    }

    private void viewDlgDadosGuiaSolicitacao(AjaxRequestTarget target, ContaPaciente contaPaciente) throws DAOException, ValidacaoException {
        if (dlgDadosGuiaSolicitacao == null) {
            addModal(target, dlgDadosGuiaSolicitacao = new DlgDadosGuiaSolicitacao(newModalId()));
        }
        dlgDadosGuiaSolicitacao.show(target, contaPaciente);
    }

    public DropDown<Long> getDropDownSituacao() {
        if (dropDownSituacao == null) {
            dropDownSituacao = DropDownUtil.getIEnumDropDown("situacao", ContaPaciente.Status.values(), true, bundle("todas"));
        }
        return dropDownSituacao;
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return ContaPaciente.class;
            }

            @Override
            public String[] getProperties() {
                ContaPaciente proxy = on(ContaPaciente.class);

                return VOUtils.mergeProperties(new HQLProperties(ContaPaciente.class).getProperties(),
                        new HQLProperties(AtendimentoInformacao.class, path(proxy.getAtendimentoInformacao())).getProperties(),
                        new String[]{
                            path(proxy.getAtendimentoInformacao().getNomePaciente()),
                            path(proxy.getAtendimentoInformacao().getDataChegada()),
                            path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getCodigo()),
                            path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getDataNascimento()),
                            path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getCpf()),
                            path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getNome()),
                            path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getApelido()),
                            path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getUtilizaNomeSocial()),
                            path(proxy.getAtendimentoInformacao().getTipoAtendimentoFaturamento().getCodigo()),
                            path(proxy.getAtendimentoInformacao().getTipoAtendimentoFaturamento().getDescricao()),
                            path(proxy.getAtendimentoInformacao().getTipoAtendimentoFaturamento().getTipoFaturamento()),
                            path(proxy.getAtendimentoInformacao().getDataSaida()),
                            path(proxy.getAtendimentoInformacao().getSequenciaCiclo()),
                            path(proxy.getUsuarioFechamento().getNome()),
                            path(proxy.getUsuarioCadsus().getDataNascimento()),
                            path(proxy.getUsuarioCadsus().getSexo()),
                            path(proxy.getAtendimentoInformacao().getEmpresa().getCodigo()),
                            path(proxy.getAtendimentoInformacao().getEmpresa().getDescricao()),
                            path(proxy.getAtendimentoInformacao().getEmpresa().getEmpresaPrincipal().getCodigo()),
                            path(proxy.getAtendimentoInformacao().getEmpresa().getEmpresaPrincipal().getDescricao()),
                            path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getCodigo()),
                            path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getNaturezaProcuraTipoAtendimento().getCodigo()),
                            path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getCodigo()),
                            path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao()),
                            path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoContaIpe()),
                            path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getEmpresa().getCodigo()),
                            path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getEmpresa().getDescricao()),
                            path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getEmpresa().getSigla()),
                            path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getProfissional().getCodigo()),
                            path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getProfissional().getNome()),
                            path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getCompetenciaAtendimento()),
                            path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getDataChegada()),
                            path(proxy.getAtendimentoInformacao().getAtendimentoAlta().getCodigo()),
                            path(proxy.getAtendimentoInformacao().getAtendimentoAlta().getDataAlta()),
                            path(proxy.getAtendimentoInformacao().getAtendimentoAlta().getMotivoAlta()),
                            path(proxy.getAtendimentoInformacao().getAtendimentoAlta().getCid().getCodigo()),
                            path(proxy.getAtendimentoInformacao().getAtendimentoAlta().getCid().getDescricao()),
                            path(proxy.getAtendimentoInformacao().getLeitoQuarto().getCodigo()),
                            path(proxy.getAtendimentoInformacao().getLeitoQuarto().getQuartoInternacao().getCodigo()),
                            path(proxy.getAtendimentoInformacao().getLeitoQuarto().getQuartoInternacao().getDescricao()),
                            path(proxy.getConvenio().getDescricao()),
                            path(proxy.getConvenio().getTipoTabelaProcedimento().getCodigo()),
                            path(proxy.getCid().getCodigo()),
                            path(proxy.getCid().getDescricao()),});
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(ContaPaciente.PROP_DATA_ABERTURA, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        ContaPaciente proxy = on(ContaPaciente.class);

        if (codigoRegistroSalvo != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getCodigo()), codigoRegistroSalvo));
            setCodigoRegistroSalvo(null);
        } else {
            if (ContaPaciente.Status.ABERTA.value().equals(situacao)) {
                parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), BuilderQueryCustom.QueryParameter.IN,
                        Arrays.asList(ContaPaciente.Status.ABERTA.value())));
            } else {
                parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), situacao));
            }

            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ContaPaciente.PROP_CONVENIO, Convenio.PROP_TIPO_TABELA_PROCEDIMENTO, TipoTabelaProcedimento.PROP_CODIGO),
                    QueryCustom.QueryCustomParameter.DIFERENTE, TipoTabelaProcedimento.Tipo.TUSS.getValue()));

            if (paciente != null) {
                parameters.add(new QueryCustom.QueryCustomParameter(
                        new BuilderQueryCustom.QueryGroupAnd(
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                        new QueryCustom.QueryCustomParameter(path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getNome()), BuilderQueryCustom.QueryParameter.CONSULTA_LIKED, paciente))),
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                        new QueryCustom.QueryCustomParameter(path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getUtilizaNomeSocial()), RepositoryComponentDefault.SIM_LONG))),
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupAnd(
                                        new QueryCustom.QueryCustomParameter(path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getApelido()), BuilderQueryCustom.QueryParameter.CONSULTA_LIKED, paciente))))));
            }
            parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getConvenio()), convenio));
            parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getCodigo()), contaPaciente));
            parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getConvenio()), BuilderQueryCustom.QueryParameter.DIFERENTE, getConvenioIpe()));
            parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getConvenio().getTipoTabelaProcedimento().getCodigo()), BuilderQueryCustom.QueryParameter.DIFERENTE, TipoTabelaProcedimento.Tipo.TUSS.getValue()));
            parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getAtendimentoInformacao().getEmpresa()), empresa));
            parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getAtendimentoInformacao().getTipoAtendimentoFaturamento()), tipoAtendimento));
            parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal()), atendimento));
            parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getCompetenciaAtendimento()), competencia));
            parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getUsuarioFechamento()), usuarioFechamento));
            parameters.add(new QueryCustom.QueryCustomParameter(tipoPeriodo, periodo));
            parameters.add(new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryRelationKeys(path(proxy.getCodigo()), path(proxy.getContaPacientePrincipal().getCodigo()))));
        }

        if (!isActionPermitted(getUsuarioLogado(), Permissions.EMPRESA, ConsultaFechamentoConta.class) && empresa == null) {
            parameters.add(new QueryCustom.QueryCustomParameter(path(proxy.getAtendimentoInformacao().getEmpresa().getCodigo()), BuilderQueryCustom.QueryParameter.IN, codigosEmpresasUsuario));
        }

        return parameters;
    }

    private Convenio getConvenioIpe() {
        if (convenioIpe == null) {
            try {
                convenioIpe = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioIPE");
            } catch (DAOException ex) {
                Logger.getLogger(ConsultaFechamentoContaIpePage.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return convenioIpe;
    }

    public DropDown getDropDownTipoPeriodo() {
        if (dropDownTipoPeriodo == null) {
            dropDownTipoPeriodo = new DropDown<String>("tipoPeriodo");
            dropDownTipoPeriodo.addChoice(VOUtils.montarPath(ContaPaciente.PROP_ATENDIMENTO_INFORMACAO, AtendimentoInformacao.PROP_DATA_CHEGADA), BundleManager.getString("dataChegada"));
            dropDownTipoPeriodo.addChoice(VOUtils.montarPath(ContaPaciente.PROP_ATENDIMENTO_INFORMACAO, AtendimentoInformacao.PROP_DATA_SAIDA), BundleManager.getString("dataSaida"));
            dropDownTipoPeriodo.addChoice(VOUtils.montarPath(ContaPaciente.PROP_DATA_FECHAMENTO), BundleManager.getString("dataFechamento"));
        }
        return dropDownTipoPeriodo;
    }

    public DropDown getDropDownConvenio() {
        if (dropDownConvenio == null) {
            dropDownConvenio = new DropDown("convenio");
            dropDownConvenio.addChoice(null, bundle("todos"));

            List<Convenio> listConvenio = LoadManager.getInstance(Convenio.class)
                    .addProperty(Convenio.PROP_CODIGO)
                    .addProperty(Convenio.PROP_DESCRICAO)
                    .addSorter(new QueryCustom.QueryCustomSorter(Convenio.PROP_DESCRICAO))
                    .addParameter(new QueryCustom.QueryCustomParameter(Convenio.PROP_CODIGO, QueryCustom.QueryCustomParameter.DIFERENTE, getConvenioIpe() != null ? getConvenioIpe().getCodigo() : null))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Convenio.PROP_TIPO_TABELA_PROCEDIMENTO, TipoTabelaProcedimento.PROP_CODIGO), BuilderQueryCustom.QueryParameter.DIFERENTE, TipoTabelaProcedimento.Tipo.TUSS.getValue()))
                    .start().getList();

            for (Convenio c : listConvenio) {
                dropDownConvenio.addChoice(c, c.getDescricao());
            }
        }
        return dropDownConvenio;
    }

    public DropDown getDropDownTipoAtendimento() {
        if (dropDownTipoAtendimento == null) {
            dropDownTipoAtendimento = new DropDown("tipoAtendimento");
            dropDownTipoAtendimento.addChoice(null, bundle("todos"));

            List<TipoAtendimento> listTipoAtendimento = LoadManager.getInstance(TipoAtendimento.class)
                    .addSorter(new QueryCustom.QueryCustomSorter(TipoAtendimento.PROP_DESCRICAO))
                    .start().getList();

            for (TipoAtendimento tipoAtendimento : listTipoAtendimento) {
                dropDownTipoAtendimento.addChoice(tipoAtendimento, tipoAtendimento.getDescricao());
            }
        }
        return dropDownTipoAtendimento;
    }

    public Usuario getUsuarioLogado() {
        return ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
    }

    @Override
    public Class getCadastroPage() {
        return CadastroFechamentoContaStep1Page.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("fechamentoContaPaciente");
    }

    public void setCodigoRegistroSalvo(Long codigoRegistroSalvo) {
        this.codigoRegistroSalvo = codigoRegistroSalvo;
        setProcurarAoAbrir(true);
    }

    private void desdobrarConta(AjaxRequestTarget target, ContaPaciente cp) throws ValidacaoException, DAOException {
        ContaPaciente proxy = on(ContaPaciente.class);

        boolean existsContaPaciente = LoadManager.getInstance(ContaPaciente.class)
                .addProperty(ContaPaciente.PROP_CODIGO)
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getCodigo()), BuilderQueryCustom.QueryParameter.DIFERENTE, cp.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getConvenio()), cp.getConvenio()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal()), cp.getAtendimentoInformacao().getAtendimentoPrincipal()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), BuilderQueryCustom.QueryParameter.IN, Arrays.asList(ContaPaciente.Status.ABERTA.value())))
                .exists();

        if (existsContaPaciente) {
            throw new ValidacaoException(bundle("msgNaoPossivelRealizarDesdobramentoContaExisteOutraContaParaMesmoAtendimentoConvenio"));
        }

        Long count = LoadManager.getInstance(AtendimentoInformacao.class)
                .addGroup(new QueryCustom.QueryCustomGroup(AtendimentoInformacao.PROP_SEQUENCIA_CICLO, BuilderQueryCustom.QueryGroup.MAX))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoInformacao.PROP_ATENDIMENTO_PRINCIPAL, Atendimento.PROP_CODIGO), cp.getAtendimentoInformacao().getAtendimentoPrincipal().getCodigo()))
                .start().getVO();

        if (!Coalesce.asLong(count).equals(cp.getAtendimentoInformacao().getSequenciaCiclo())) {
            throw new ValidacaoException(bundle("msgEssaContaJaFoiDesdobrada"));
        }

        BOFactoryWicket.getBO(HospitalFacade.class).desdobrarContaPaciente(cp);

        initDialogImpressao(target);
        dlgConfirmacaoImpressaoProntuario.show(target, cp);
    }

    private void initDialogImpressao(AjaxRequestTarget target) {
        if (dlgConfirmacaoImpressaoProntuario == null) {
            dlgConfirmacaoImpressaoProntuario = new DlgImpressaoObject<ContaPaciente>(newModalId(), bundle("desdobramentoContaRealizadoSucesso")) {
                @Override
                public DataReport getDataReport(ContaPaciente contaPaciente) throws ReportException {
                    RelatorioImpressaoAtendimentoDTOParam param = new RelatorioImpressaoAtendimentoDTOParam();
                    param.setAtendimento(contaPaciente.getAtendimentoInformacao().getAtendimentoPrincipal());
                    param.setCorrecao(RepositoryComponentDefault.NAO_LONG);
                    param.setCiclo(Coalesce.asLong(contaPaciente.getAtendimentoInformacao().getSequenciaCiclo()));

                    return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoAtendimentoInternacao(param);
                }

                @Override
                public void onFechar(AjaxRequestTarget target, ContaPaciente contaPaciente) throws ValidacaoException, DAOException {
                    close(target);
                    getPageableTable().update(target);
                }
            };
            addModal(target, dlgConfirmacaoImpressaoProntuario);
        }
    }

    @Override
    public PageParameters getPageParameters() {
        PageParameters parametro = new PageParameters();
        parametro.set(CadastroFechamentoContaDTO.PROP_ORIGEM_CONTA, CadastroFechamentoContaDTO.ORIGEM_CONTA_NORMAL);
        return parametro;
    }
}
