package br.com.celk.view.unidadesaude.bpa.relatorio;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.prontuario.procedimento.autocomplete.AutoCompleteConsultaProcedimento;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioConferenciaBPADTOParam;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.facade.ProcedimentoReportFacade;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.atendimento.Bpa;
import br.com.ksisolucoes.vo.atendimento.BpaProcesso;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import java.util.Date;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class RelatorioConferenciaBPAPage extends RelatorioPage<RelatorioConferenciaBPADTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private String mesAno;
    private DropDown dropDownTipo;
    private DropDown<String> dropDownVisualizarAtendimentos;
    private DropDown<String> dropDownFormaApresentacao;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresa")
                .setValidarTipoEstabelecimento(true)
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaProfissional("profissional")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaTabelaCbo("tabelaCbo")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(dropDownTipo = DropDownUtil.getEnumDropDown("tipo", Bpa.TIPO_BPA.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoFinanciamento", BpaProcesso.TipoFinanciamento.values()));
        form.add(getDropDownFormaApresentacao());
        form.add(dropDownVisualizarAtendimentos = DropDownUtil.getNaoSimDropDown("visualizarAtendimentos"));
        form.add(new RequiredInputField("mesAno", new PropertyModel(this, "mesAno")));

        this.dropDownTipo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (dropDownTipo.getComponentValue() != null) {
                    if (dropDownTipo.getComponentValue().equals(Bpa.TIPO_BPA.INDIVIDUAL)) {
                        dropDownVisualizarAtendimentos.setEnabled(false);
                        dropDownVisualizarAtendimentos.setComponentValue(RepositoryComponentDefault.NAO);
                    } else {
                        dropDownVisualizarAtendimentos.setEnabled(true);
                    }
                    target.add(dropDownVisualizarAtendimentos);
                    defineDropDownFA();
                    target.add(dropDownFormaApresentacao);
                }
            }
        });
        
        form.add(new AutoCompleteConsultaProcedimento("procedimento"));
    }

    public DropDown getDropDownFormaApresentacao() {
        defineDropDownFA();
        return dropDownFormaApresentacao;
    }

    private void defineDropDownFA() {
        if (dropDownFormaApresentacao == null) {
            dropDownFormaApresentacao = new DropDown<String>("formaApresentacao");
        } else {
            dropDownFormaApresentacao.removeAllChoices();
        }

        if (Bpa.TIPO_BPA.INDIVIDUAL.equals(dropDownTipo.getComponentValue())) {
            dropDownFormaApresentacao.addChoice(Profissional.REF, BundleManager.getString("profissional"));
            dropDownFormaApresentacao.setComponentValue(Profissional.REF);
        } else {
            dropDownFormaApresentacao.addChoice(TabelaCbo.REF, BundleManager.getString("cbo"));
            if (dropDownFormaApresentacao.getModel() != null) {
                dropDownFormaApresentacao.setComponentValue(TabelaCbo.REF);
            }
        }

        dropDownFormaApresentacao.addChoice(BundleManager.getString("folha"), BundleManager.getString("folha"));
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioConferenciaBPADTOParam.class;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    @Override
    public DataReport getDataReport(RelatorioConferenciaBPADTOParam param) throws ReportException {
        if (param.getTipo().equals(Bpa.TIPO_BPA.INDIVIDUAL.value())) {
            param.setFormaApresentacao("");
        }
        Date data = Data.parserMounthYear(mesAno);
        param.setMesAno(Data.adjustRangeDay(data).getDataInicial());
        return BOFactoryWicket.getBO(ProcedimentoReportFacade.class).relatorioConferenciaBPA(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("conferenciaBpa");
    }
}
