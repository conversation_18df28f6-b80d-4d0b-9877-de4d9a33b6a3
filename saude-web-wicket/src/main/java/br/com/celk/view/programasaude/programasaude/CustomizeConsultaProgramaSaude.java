/*
 * Copyright 2012 joao.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package br.com.celk.view.programasaude.programasaude;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.programasaude.ProgramaSaude;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaProgramaSaude extends CustomizeConsultaAdapter {

    @Override
    public void consultaCustomizeFilterProperties(Map<String, BuilderQueryCustom.QueryParameter> filterProperties) {
        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProgramaSaude.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(BundleManager.getString("descricao"), VOUtils.montarPath(ProgramaSaude.PROP_DESCRICAO));
    }

    @Override
    public Class getClassConsulta() {
        return ProgramaSaude.class;
    }

    @Override
    public String[] getProperties() {
        return new HQLProperties(ProgramaSaude.class).getProperties();
    }
    
}
    
