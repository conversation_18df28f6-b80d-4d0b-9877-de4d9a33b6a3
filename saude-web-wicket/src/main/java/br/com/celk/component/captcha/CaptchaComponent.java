package br.com.celk.component.captcha;

import br.com.celk.component.inputfield.InputField;
import br.com.celk.util.CaptchaSession;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.resource.CssResourceReference;
import org.apache.wicket.validation.IValidatable;
import org.apache.wicket.validation.IValidator;
import org.apache.wicket.validation.ValidationError;

/**
 * Componente CAPTCHA para validação anti-bot
 * 
 * <AUTHOR>
 */
public class CaptchaComponent extends Panel {
    
    private static final long serialVersionUID = 1L;
    
    private InputField<String> captchaInput;
    private Image captchaImage;
    private CaptchaImageResource imageResource;
    
    public CaptchaComponent(String id) {
        super(id);
        initComponents();
    }
    
    private void initComponents() {
        // Recurso para gerar imagem CAPTCHA
        imageResource = new CaptchaImageResource();
        
        // Imagem CAPTCHA
        captchaImage = new Image("captchaImage", imageResource);
        captchaImage.setOutputMarkupId(true);
        add(captchaImage);
        
        // Campo de entrada do CAPTCHA
        captchaInput = new InputField<String>("captchaInput", new Model<String>()) {
            @Override
            protected void onInitialize() {
                super.onInitialize();
                setRequired(true);
                add(new CaptchaValidator());
            }
        };
        captchaInput.setOutputMarkupId(true);
        add(captchaInput);
        
        // Link para renovar CAPTCHA
        add(new AjaxLink<Void>("refreshCaptcha") {
            @Override
            public void onClick(AjaxRequestTarget target) {
                refreshCaptcha(target);
            }
        });
    }
    
    /**
     * Renova a imagem CAPTCHA
     */
    public void refreshCaptcha(AjaxRequestTarget target) {
        // Limpar campo de entrada
        captchaInput.setModelObject(null);
        
        // Forçar regeneração da imagem
        captchaImage.setImageResource(new CaptchaImageResource());
        
        if (target != null) {
            target.add(captchaImage);
            target.add(captchaInput);
        }
    }
    
    /**
     * Valida o CAPTCHA informado pelo usuário
     * 
     * @return true se o CAPTCHA é válido
     * @throws ValidacaoException se o CAPTCHA é inválido
     */
    public boolean validateCaptcha() throws ValidacaoException {
        String userInput = captchaInput.getModelObject();
        
        if (userInput == null || userInput.trim().isEmpty()) {
            throw new ValidacaoException("Por favor, digite o código de verificação.");
        }
        
        if (!CaptchaSession.validateCaptcha(userInput)) {
            throw new ValidacaoException("Código de verificação inválido. Tente novamente.");
        }
        
        return true;
    }
    
    /**
     * Limpa o campo de entrada do CAPTCHA
     */
    public void clearInput() {
        captchaInput.setModelObject(null);
    }
    
    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(new CssResourceReference(CaptchaComponent.class, "captcha.css")));
    }

    /**
     * Validator interno para o campo CAPTCHA
     */
    private class CaptchaValidator implements IValidator<String> {

        @Override
        public void validate(IValidatable<String> validatable) {
            String userInput = validatable.getValue();

            if (userInput == null || userInput.trim().isEmpty()) {
                ValidationError error = new ValidationError();
                error.setMessage("Por favor, digite o código de verificação.");
                validatable.error(error);
                return;
            }

            // Validação será feita no momento do submit do formulário
            // para evitar validação prematura durante digitação
        }
    }
}
