package br.com.celk.component.captcha;

import br.com.celk.component.inputfield.InputField;
import br.com.celk.util.CaptchaSession;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.resource.DynamicImageResource;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * Versão simplificada do componente CAPTCHA
 * 
 * <AUTHOR>
 */
public class SimpleCaptchaComponent extends Panel {
    
    private static final long serialVersionUID = 1L;
    
    private InputField<String> captchaInput;
    private Image captchaImage;
    private Label debugLabel;
    
    public SimpleCaptchaComponent(String id) {
        super(id);
        initComponents();
    }
    
    private void initComponents() {
        // Label de debug
        debugLabel = new Label("debugLabel", new Model<String>("CAPTCHA carregando..."));
        debugLabel.setOutputMarkupId(true);
        add(debugLabel);
        
        // Imagem CAPTCHA simples
        captchaImage = new Image("captchaImage", new SimpleCaptchaImageResource());
        captchaImage.setOutputMarkupId(true);
        add(captchaImage);
        
        // Campo de entrada do CAPTCHA
        captchaInput = new InputField<String>("captchaInput", new Model<String>());
        captchaInput.setOutputMarkupId(true);
        captchaInput.setRequired(true);
        add(captchaInput);
        
        // Link para renovar CAPTCHA
        add(new AjaxLink<Void>("refreshCaptcha") {
            @Override
            public void onClick(AjaxRequestTarget target) {
                refreshCaptcha(target);
            }
        });
    }
    
    /**
     * Renova a imagem CAPTCHA
     */
    public void refreshCaptcha(AjaxRequestTarget target) {
        // Limpar campo de entrada
        captchaInput.setModelObject(null);
        
        // Atualizar debug
        debugLabel.setDefaultModelObject("CAPTCHA renovado em: " + System.currentTimeMillis());
        
        // Forçar regeneração da imagem
        captchaImage.setImageResource(new SimpleCaptchaImageResource());
        
        if (target != null) {
            target.add(captchaImage);
            target.add(captchaInput);
            target.add(debugLabel);
        }
    }
    
    /**
     * Valida o CAPTCHA informado pelo usuário
     */
    public boolean validateCaptcha() throws ValidacaoException {
        String userInput = captchaInput.getModelObject();
        
        if (userInput == null || userInput.trim().isEmpty()) {
            throw new ValidacaoException("Por favor, digite o código de verificação.");
        }
        
        if (!CaptchaSession.validateCaptcha(userInput)) {
            throw new ValidacaoException("Código de verificação inválido. Tente novamente.");
        }
        
        return true;
    }
    
    /**
     * Recurso de imagem simplificado
     */
    private static class SimpleCaptchaImageResource extends DynamicImageResource {
        
        @Override
        protected byte[] getImageData(Attributes attributes) {
            try {
                // Gerar código CAPTCHA
                String captchaCode = CaptchaSession.generateNewCaptcha();
                System.out.println("DEBUG SimpleCaptcha: Código gerado: " + captchaCode);
                
                // Criar imagem simples
                BufferedImage image = createSimpleImage(captchaCode);
                
                // Converter para bytes
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(image, "PNG", baos);
                byte[] result = baos.toByteArray();
                
                System.out.println("DEBUG SimpleCaptcha: Imagem criada, tamanho: " + result.length + " bytes");
                return result;
                
            } catch (Exception e) {
                System.err.println("ERRO SimpleCaptcha: " + e.getMessage());
                e.printStackTrace();
                
                // Retornar imagem de erro
                return createErrorImage();
            }
        }
        
        private BufferedImage createSimpleImage(String text) {
            BufferedImage image = new BufferedImage(200, 60, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = image.createGraphics();
            
            // Fundo branco
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, 200, 60);
            
            // Texto preto simples
            g2d.setColor(Color.BLACK);
            g2d.setFont(new Font("Arial", Font.BOLD, 24));
            g2d.drawString(text, 50, 35);
            
            // Borda
            g2d.setColor(Color.GRAY);
            g2d.drawRect(0, 0, 199, 59);
            
            g2d.dispose();
            return image;
        }
        
        private byte[] createErrorImage() {
            try {
                BufferedImage image = new BufferedImage(200, 60, BufferedImage.TYPE_INT_RGB);
                Graphics2D g2d = image.createGraphics();
                
                g2d.setColor(Color.RED);
                g2d.fillRect(0, 0, 200, 60);
                
                g2d.setColor(Color.WHITE);
                g2d.setFont(new Font("Arial", Font.BOLD, 16));
                g2d.drawString("ERRO", 80, 35);
                
                g2d.dispose();
                
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(image, "PNG", baos);
                return baos.toByteArray();
            } catch (IOException e) {
                return new byte[0];
            }
        }
        
        @Override
        protected void setResponseHeaders(ResourceResponse data, Attributes attributes) {
            super.setResponseHeaders(data, attributes);
            data.setContentType("image/png");
            data.disableCaching();
        }
    }
}
