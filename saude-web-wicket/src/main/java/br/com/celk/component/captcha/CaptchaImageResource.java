package br.com.celk.component.captcha;

import br.com.celk.util.CaptchaGenerator;
import br.com.celk.util.CaptchaSession;
import org.apache.wicket.request.resource.DynamicImageResource;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * Recurso para gerar e servir imagens CAPTCHA
 * 
 * <AUTHOR>
 */
public class CaptchaImageResource extends DynamicImageResource {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    protected byte[] getImageData(Attributes attributes) {
        // Gerar novo código CAPTCHA
        String captchaCode = CaptchaSession.generateNewCaptcha();
        
        // Gerar imagem
        BufferedImage image = CaptchaGenerator.generateCaptchaImage(captchaCode);
        
        // Converter para bytes
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            ImageIO.write(image, "PNG", baos);
            return baos.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException("Erro ao gerar imagem CAPTCHA", e);
        }
    }
    
    @Override
    protected void setResponseHeaders(ResourceResponse data, Attributes attributes) {
        super.setResponseHeaders(data, attributes);
        // Evitar cache da imagem CAPTCHA
        data.setContentType("image/png");
        data.setCacheDuration(null);
        data.disableCaching();
    }
}
