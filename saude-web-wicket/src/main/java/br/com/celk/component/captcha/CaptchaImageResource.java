package br.com.celk.component.captcha;

import br.com.celk.util.CaptchaGenerator;
import br.com.celk.util.CaptchaSession;
import org.apache.wicket.request.resource.DynamicImageResource;
import org.apache.wicket.util.time.Duration;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;

/**
 * Recurso para gerar e servir imagens CAPTCHA
 * 
 * <AUTHOR>
 */
public class CaptchaImageResource extends DynamicImageResource {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    protected byte[] getImageData(Attributes attributes) {
        try {
            // Gerar novo código CAPTCHA
            String captchaCode = CaptchaSession.generateNewCaptcha();
            System.out.println("DEBUG: Gerando CAPTCHA com código: " + captchaCode);

            // Gerar imagem
            BufferedImage image = CaptchaGenerator.generateCaptchaImage(captchaCode);
            System.out.println("DEBUG: Imagem gerada com dimensões: " + image.getWidth() + "x" + image.getHeight());

            // Converter para bytes
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                ImageIO.write(image, "PNG", baos);
                byte[] imageData = baos.toByteArray();
                System.out.println("DEBUG: Imagem convertida para bytes, tamanho: " + imageData.length);
                return imageData;
            }
        } catch (Exception e) {
            System.err.println("ERRO ao gerar imagem CAPTCHA: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Erro ao gerar imagem CAPTCHA", e);
        }
    }
    
    @Override
    protected void setResponseHeaders(ResourceResponse data, Attributes attributes) {
        super.setResponseHeaders(data, attributes);
        // Evitar cache da imagem CAPTCHA
        data.setContentType("image/png");
        data.setCacheDuration(Duration.NONE);
        data.disableCaching();
        data.setLastModified(null);
        // Adicionar headers para evitar cache
        data.getHeaders().setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        data.getHeaders().setHeader("Pragma", "no-cache");
        data.getHeaders().setHeader("Expires", "0");
    }
}
