/* Estilos para o componente CAPTCHA */

.captcha-container {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    background-color: #f9f9f9;
    margin: 10px 0;
    max-width: 300px;
}

.captcha-image-container {
    text-align: center;
    margin-bottom: 10px;
}

.captcha-image-container img {
    border: 2px solid #ccc;
    border-radius: 3px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.captcha-image-container a {
    display: inline-block;
    margin-left: 10px;
    padding: 5px 10px;
    background-color: #007cba;
    color: white;
    text-decoration: none;
    border-radius: 3px;
    font-size: 12px;
    vertical-align: middle;
}

.captcha-image-container a:hover {
    background-color: #005a87;
}

.captcha-input-container {
    margin-bottom: 10px;
}

.captcha-input-container label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.captcha-input-container input {
    width: 100px;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 3px;
    text-transform: uppercase;
    font-family: monospace;
    font-size: 14px;
    text-align: center;
}

.captcha-input-container input:focus {
    border-color: #007cba;
    outline: none;
    box-shadow: 0 0 5px rgba(0, 124, 186, 0.3);
}

.captcha-help {
    font-size: 11px;
    color: #666;
    font-style: italic;
}

/* Responsividade */
@media (max-width: 480px) {
    .captcha-container {
        max-width: 100%;
    }
    
    .captcha-image-container a {
        display: block;
        margin: 10px auto 0;
        text-align: center;
        width: 80px;
    }
}
