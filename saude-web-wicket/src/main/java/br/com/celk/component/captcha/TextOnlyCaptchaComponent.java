package br.com.celk.component.captcha;

import br.com.celk.component.inputfield.InputField;
import br.com.celk.util.CaptchaSession;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;

/**
 * Componente CAPTCHA apenas com texto (fallback para ambientes sem suporte a imagens)
 * 
 * <AUTHOR>
 */
public class TextOnlyCaptchaComponent extends Panel {
    
    private static final long serialVersionUID = 1L;
    
    private InputField<String> captchaInput;
    private Label captchaLabel;
    
    public TextOnlyCaptchaComponent(String id) {
        super(id);
        initComponents();
    }
    
    private void initComponents() {
        // Label com o código CAPTCHA
        captchaLabel = new Label("captchaLabel", new Model<String>());
        captchaLabel.setOutputMarkupId(true);
        generateNewCode();
        add(captchaLabel);
        
        // Campo de entrada do CAPTCHA
        captchaInput = new InputField<String>("captchaInput", new Model<String>());
        captchaInput.setOutputMarkupId(true);
        captchaInput.setRequired(true);
        add(captchaInput);
        
        // Link para renovar CAPTCHA
        add(new AjaxLink<Void>("refreshCaptcha") {
            @Override
            public void onClick(AjaxRequestTarget target) {
                refreshCaptcha(target);
            }
        });
    }
    
    private void generateNewCode() {
        String code = CaptchaSession.generateNewCaptcha();
        // Adicionar espaços entre caracteres para dificultar cópia
        String displayCode = code.replaceAll("(.)", "$1 ");
        captchaLabel.setDefaultModelObject("Código: " + displayCode);
    }
    
    /**
     * Renova o código CAPTCHA
     */
    public void refreshCaptcha(AjaxRequestTarget target) {
        // Limpar campo de entrada
        captchaInput.setModelObject(null);
        
        // Gerar novo código
        generateNewCode();
        
        if (target != null) {
            target.add(captchaLabel);
            target.add(captchaInput);
        }
    }
    
    /**
     * Valida o CAPTCHA informado pelo usuário
     */
    public boolean validateCaptcha() throws ValidacaoException {
        String userInput = captchaInput.getModelObject();
        
        if (userInput == null || userInput.trim().isEmpty()) {
            throw new ValidacaoException("Por favor, digite o código de verificação.");
        }
        
        if (!CaptchaSession.validateCaptcha(userInput)) {
            throw new ValidacaoException("Código de verificação inválido. Tente novamente.");
        }
        
        return true;
    }
}
