# Componente CAPTCHA

Este componente implementa um sistema de CAPTCHA para prevenir acesso automatizado de bots em páginas públicas.

## Funcionalidades

- Geração de códigos CAPTCHA aleatórios com 5 caracteres (letras e números)
- Imagens com distorções, ruído e linhas de interferência
- Validação case-insensitive
- Timeout de 5 minutos para expiração do CAPTCHA
- Uso único (CAPTCHA é invalidado após validação bem-sucedida)
- Renovação automática da imagem

## Como usar

### 1. Adicionar o componente à sua página

```java
// Na classe da página
private CaptchaComponent captchaComponent;

// No método onInitialize()
captchaComponent = new CaptchaComponent("captcha");
form.add(captchaComponent);
```

### 2. Adicionar ao template HTML

```html
<div wicket:id="captcha"></div>
```

### 3. Validar o CAPTCHA

```java
// Antes de executar a ação protegida
try {
    captchaComponent.validateCaptcha();
    // Continuar com a ação
} catch (ValidacaoException e) {
    // Renovar CAPTCHA em caso de erro
    captchaComponent.refreshCaptcha(target);
    throw e;
}
```

## Exemplo de implementação

Veja a implementação na `ConsultaMedicamentoPublicoPage` como exemplo de uso completo.

## Configurações

### Timeout do CAPTCHA
O timeout padrão é de 5 minutos. Para alterar, modifique a constante `CAPTCHA_TIMEOUT` na classe `CaptchaSession`.

### Tamanho da imagem
Para alterar o tamanho da imagem, modifique as constantes `IMAGE_WIDTH` e `IMAGE_HEIGHT` na classe `CaptchaGenerator`.

### Comprimento do código
Para alterar o número de caracteres, modifique a constante `CAPTCHA_LENGTH` na classe `CaptchaGenerator`.

## Segurança

- O código CAPTCHA é armazenado na sessão do usuário
- Cada CAPTCHA tem uso único
- Timeout automático para prevenir ataques de força bruta
- Imagem gerada dinamicamente a cada requisição

## Acessibilidade

Para melhorar a acessibilidade, considere:
- Adicionar texto alternativo descritivo
- Implementar versão em áudio (futuro)
- Fornecer opções de renovação fáceis
