package br.com.celk.util;

import org.junit.Test;

import java.awt.image.BufferedImage;

import static org.junit.Assert.*;

/**
 * Teste unitário para o gerador de CAPTCHA
 * 
 * <AUTHOR>
 */
public class CaptchaGeneratorTest {
    
    @Test
    public void testGenerateCaptchaCode() {
        String captcha = CaptchaGenerator.generateCaptchaCode();
        
        assertNotNull("CAPTCHA não deve ser nulo", captcha);
        assertEquals("CAPTCHA deve ter 5 caracteres", 5, captcha.length());
        assertTrue("CAPTCHA deve conter apenas letras e números", 
                   captcha.matches("[A-Z0-9]+"));
    }
    
    @Test
    public void testGenerateCaptchaImage() {
        String captchaCode = "TEST1";
        BufferedImage image = CaptchaGenerator.generateCaptchaImage(captchaCode);
        
        assertNotNull("Imagem não deve ser nula", image);
        assertEquals("Largura da imagem deve ser 200", 200, image.getWidth());
        assertEquals("Altura da imagem deve ser 60", 60, image.getHeight());
    }
    
    @Test
    public void testValidateCaptcha() {
        String correctCode = "ABCD1";
        
        // Teste com código correto
        assertTrue("Validação deve passar com código correto", 
                   CaptchaGenerator.validateCaptcha("ABCD1", correctCode));
        
        // Teste case insensitive
        assertTrue("Validação deve ser case insensitive", 
                   CaptchaGenerator.validateCaptcha("abcd1", correctCode));
        
        // Teste com espaços
        assertTrue("Validação deve ignorar espaços", 
                   CaptchaGenerator.validateCaptcha(" ABCD1 ", correctCode));
        
        // Teste com código incorreto
        assertFalse("Validação deve falhar com código incorreto", 
                    CaptchaGenerator.validateCaptcha("WRONG", correctCode));
        
        // Teste com valores nulos
        assertFalse("Validação deve falhar com entrada nula", 
                    CaptchaGenerator.validateCaptcha(null, correctCode));
        assertFalse("Validação deve falhar com código correto nulo", 
                    CaptchaGenerator.validateCaptcha("ABCD1", null));
    }
    
//    @Test
//    public void testGenerateMultipleCaptchas() {
//        String captcha1 = CaptchaGenerator.generateCaptchaCode();
//        String captcha2 = CaptchaGenerator.generateCaptchaCode();
//
        // É muito improvável que dois CAPTCHAs sejam iguais
//        assertNotEquals("CAPTCHAs devem ser diferentes", captcha1, captcha2);
//    }
}
