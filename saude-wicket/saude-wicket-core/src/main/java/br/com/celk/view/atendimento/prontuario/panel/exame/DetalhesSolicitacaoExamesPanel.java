package br.com.celk.view.atendimento.prontuario.panel.exame;

import br.com.celk.atendimento.prontuario.exames.interfaces.dto.DetalhesSolicitacaoExamesDTO;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.atendimento.prontuario.panel.*;
import br.com.celk.view.atendimento.prontuario.panel.exame.tabbedpanel.DetalhesExameMamografiaTab;
import br.com.celk.view.atendimento.prontuario.panel.exame.tabbedpanel.DetalhesExamePreventivoTab;
import br.com.celk.view.atendimento.prontuario.panel.exame.tabbedpanel.DetalhesExameTab;
import br.com.celk.view.atendimento.prontuario.panel.exame.tabbedpanel.DetalhesHistoricoSolicitacaoExamesTabbedPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.SoapDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.tabs.ITab;
import org.apache.wicket.markup.html.form.Form;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 *
 * <AUTHOR>
 */
public class DetalhesSolicitacaoExamesPanel extends ProntuarioCadastroPanel {

    private Long codigoExameRequisicao;
    private DetalhesSolicitacaoExamesDTO dto;
    private SoapDTO.ContainerTelaSoap containerTelaSoap;

    public DetalhesSolicitacaoExamesPanel(String id, Long codigoExameRequisicao) {
        super(id, bundle("detalhesExame"));
        this.codigoExameRequisicao = codigoExameRequisicao;
    }

    public DetalhesSolicitacaoExamesPanel(String id, Long codigoExameRequisicao, SoapDTO.ContainerTelaSoap containerTelaSoap) {
        super(id, bundle("detalhesExame"));
        this.codigoExameRequisicao = codigoExameRequisicao;
        this.containerTelaSoap = containerTelaSoap;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();

        Form form = new Form("form");

        try {
            dto = BOFactoryWicket.getBO(AtendimentoFacade.class).carregarDetalhesSolicitacaoExames(codigoExameRequisicao);
        } catch (DAOException ex) {
            Logger.getLogger(DetalhesSolicitacaoExamesPanel.class.getName()).log(Level.SEVERE, null, ex);
        } catch (ValidacaoException ex) {
            Logger.getLogger(DetalhesSolicitacaoExamesPanel.class.getName()).log(Level.SEVERE, null, ex);
        }

        List<ITab> tabs = new ArrayList<ITab>();
        tabs.add(new CadastroTab<DetalhesSolicitacaoExamesDTO>(dto) {

            @Override
            public ITabPanel<DetalhesSolicitacaoExamesDTO> newTabPanel(String id, DetalhesSolicitacaoExamesDTO dto) {
                return new DetalhesExameTab(id, dto);
            }
        });

        if (dto.getPreventivo() != null) {
            tabs.add(new CadastroTab<DetalhesSolicitacaoExamesDTO>(dto) {

                @Override
                public ITabPanel<DetalhesSolicitacaoExamesDTO> newTabPanel(String id, DetalhesSolicitacaoExamesDTO dto) {
                    return new DetalhesExamePreventivoTab(id, dto);
                }
            });

            if (dto.getPreventivo().getResultadoSaudeMulher() != null) {
                tabs.add(new CadastroTab<DetalhesSolicitacaoExamesDTO>(dto) {
                    @Override
                    public ITabPanel<DetalhesSolicitacaoExamesDTO> newTabPanel(String panelId, DetalhesSolicitacaoExamesDTO object) {
                        return new ResultadoPreventivoTab(panelId, object);
                    }
                });
            }
        }

        if (dto.getMamografia() != null) {
            tabs.add(new CadastroTab<DetalhesSolicitacaoExamesDTO>(dto) {

                @Override
                public ITabPanel<DetalhesSolicitacaoExamesDTO> newTabPanel(String id, DetalhesSolicitacaoExamesDTO dto) {
                    return new DetalhesExameMamografiaTab(id, dto);
                }
            });
        }

        form.add(new DetalhesHistoricoSolicitacaoExamesTabbedPanel("wizard", dto, true, tabs, false));

        form.add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                Long tipo = dto.getExameRequisicao().getExame().getTipoExame().getTipo();
                if (TipoExame.Tipo.PADRAO.value().equals(tipo)) {
                    if(containerTelaSoap != null && RepositoryComponentDefault.NO_SOAP.equals(containerTelaSoap.descricao())){
                        getProntuarioController().changePanel(target, new SoapPanel(getProntuarioController().panelId(), BundleManager.getString("evolucaoSoap"), containerTelaSoap));
                    } else if(containerTelaSoap != null && RepositoryComponentDefault.NO_REGISTRO_ESPECIALIZADO.equals(containerTelaSoap.descricao())){
                        getProntuarioController().changePanel(target, new RegistroEspecializadoPanel(getProntuarioController().panelId(), BundleManager.getString("registroEspecializado"), containerTelaSoap));
                    } else {
                        getProntuarioController().changePanel(target, new SolicitacaoExamesPanel(getProntuarioController().panelId(), containerTelaSoap));
                    }
                } else if (TipoExame.Tipo.LACEN.value().equals(tipo)) {
                    getProntuarioController().changePanel(target, new SolicitacaoLacenPanel(getProntuarioController().panelId()));
                } else if (TipoExame.Tipo.SAUDE_MULHER.value().equals(tipo)) {
                    getProntuarioController().changePanel(target, new SaudeMulherPanel(getProntuarioController().panelId()));
                }
            }
        });

        add(form);
    }
}
