package br.com.celk.view.materiais.produto.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.materiais.produto.autocomplete.restricaocontainer.RestricaoContainerProdutoSolicitado;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaProdutoSolicitadoItemDTOParam;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.ProdutoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaProdutoSolicitadoItem extends AutoCompleteConsulta<ProdutoSolicitadoItem> {

    private Long status;
    private UsuarioCadsus usuarioCadsus;
    private ProdutoSolicitado produtoSolicitado;

    public AutoCompleteConsultaProdutoSolicitadoItem(String id) {
        super(id);
    }

    public AutoCompleteConsultaProdutoSolicitadoItem(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaProdutoSolicitadoItem(String id, IModel<ProdutoSolicitadoItem> model) {
        super(id, model);
    }

    public AutoCompleteConsultaProdutoSolicitadoItem(String id, IModel<ProdutoSolicitadoItem> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public int getMinDialogHeight() {
        return 500;
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(ProdutoSolicitadoItem.class);

                columns.add(columnFactory.createSortableColumn(BundleManager.getString("produto"), VOUtils.montarPath(ProdutoSolicitadoItem.PROP_PRODUTO, Produto.PROP_DESCRICAO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("paciente"), VOUtils.montarPath(ProdutoSolicitadoItem.PROP_PRODUTO_SOLICITADO, ProdutoSolicitado.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_SOCIAL)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerProdutoSolicitado(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<ProdutoSolicitadoItem, QueryConsultaProdutoSolicitadoItemDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaProdutoSolicitadoItemDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(ProdutoFacade.class).consultarProdutoSolicitado(dataPaging);
                    }

                    @Override
                    public QueryConsultaProdutoSolicitadoItemDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaProdutoSolicitadoItemDTOParam param = new QueryConsultaProdutoSolicitadoItemDTOParam();
                        param.setKeyword(searchCriteria);
                        return param;
                    }

                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(ProdutoSolicitadoItem.PROP_PRODUTO, Produto.PROP_DESCRICAO), true);
                    }

                    @Override
                    public void customizeParam(QueryConsultaProdutoSolicitadoItemDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                        param.setStatus(status);
                        param.setUsuarioCadsus(usuarioCadsus);
                        param.setProdutoSolicitado(produtoSolicitado);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return ProdutoSolicitadoItem.class;
            }

        };
    }

    @Override
    public String[] getPropertiesLoad() {
        ProdutoSolicitadoItem proxy = on(ProdutoSolicitadoItem.class);
        
        return VOUtils.mergeProperties(new HQLProperties(ProdutoSolicitadoItem.class).getProperties(),
                new HQLProperties(ProdutoSolicitado.class, path(proxy.getProdutoSolicitado())).getProperties(),
                new HQLProperties(UsuarioCadsus.class, path(proxy.getProdutoSolicitado().getUsuarioCadsus())).getProperties(),
                new HQLProperties(AssistenteSocial.class, path(proxy.getProdutoSolicitado().getAssistenteSocial())).getProperties(),
                new HQLProperties(Profissional.class, path(proxy.getProdutoSolicitado().getProfissional())).getProperties(),
                new HQLProperties(TipoSolicitacaoProduto.class, path(proxy.getProdutoSolicitado().getTipoSolicitacaoProduto())).getProperties(),
                new HQLProperties(Empresa.class, path(proxy.getProdutoSolicitado().getEmpresa())).getProperties(),
                new HQLProperties(UsuarioCadsus.class, path(proxy.getProdutoSolicitado().getUsuarioCadsus())).getProperties(),
                new HQLProperties(SubGrupo.class, path(proxy.getProduto().getSubGrupo())).getProperties(),
                new HQLProperties(Produto.class, path(proxy.getProduto())).getProperties());
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("produtos");
    }

    public AutoCompleteConsultaProdutoSolicitadoItem setStatus(Long status) {
        this.status = status;
        return this;
    }
    
    public AutoCompleteConsultaProdutoSolicitadoItem setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
        return this;
    }

    public AutoCompleteConsultaProdutoSolicitadoItem setProdutoSolicitado(ProdutoSolicitado produtoSolicitado){
        this.produtoSolicitado = produtoSolicitado;
        return this;
    }
}
