package br.com.celk.component.wizard.cadastro;

import br.com.celk.component.wizard.AjaxWizardStep;

/**
 *
 * <AUTHOR>
 */
public abstract class CadastroWizardStep<T> extends AjaxWizardStep{

    protected T object;

    public CadastroWizardStep() {
        init();
    }
    
    public abstract void init();
    
    public final CadastroWizardStep<T> setObject(T object){
        this.object = object;
        onSetObject(this.object);
        return this;
    }
    
    public void onSetObject(T object){}
    
}
