<wicket:panel>
    <form wicket:id="form">
        <div class="span-10 last">
            <fieldset>
                <div class="field">
                    <label style="font-size: 14px">
                        <wicket:message key="paciente"/>
                    </label>
                    <input type="text" style="font-size: 14px; width: 398px;" maxlength="60"
                           wicket:id="nomeUsuarioCadsus"/>
                </div>
            </fieldset>
            <div wicket:id="containerSenhaDiv">
                <fieldset>
                    <div class="field">
                        <label style="font-size: 14px">
                            <wicket:message key="senha"/>
                        </label>
                        <input class="number" maxlength="4" wicket:id="senha"
                               style="font-size: 14px; width: 398px;"/>

                    </div>

                    <div class="group" wicket:id="tipoSenha">
                        <div class="field">
                            <label><wicket:message key="atendimento" /></label>
                            <label style="width: initial;"><input wicket:id="radioAtendimentoComumButton" type="radio"/>
                                <wicket:message key="rotulo_comum_prioridade_atendimento"/>
                            </label>
                            <label style="width: initial;"><input wicket:id="radioAtendimentoPrioritarioButton"
                                                                  type="radio"/>
                                <wicket:message key="rotulo_prioridade_atendimento"/>
                            </label>
                        </div>
                    </div>
                </fieldset>
            </div>
        </div>
        <div class="span-10 last">
            <div id="control-bottom">
                <input type="button" wicket:id="btnConfirmar" class="save" wicket:message="value:confirmar"/>
                <input type="button" wicket:id="btnFechar" class="delete" wicket:message="value:fechar"/>
            </div>
        </div>
    </form>
</wicket:panel>