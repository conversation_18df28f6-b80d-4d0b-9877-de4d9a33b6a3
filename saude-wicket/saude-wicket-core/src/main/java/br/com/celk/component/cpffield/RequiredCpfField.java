package br.com.celk.component.cpffield;

import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class RequiredCpfField extends Cpf<PERSON>ield {

    public RequiredCpfField(String id) {
        super(id);
        init();
    }

    public RequiredCpfField(String id, IModel<String> model) {
        super(id, model);
        init();
    }
    
    private void init(){
        setRequired(true);
        addRequiredClass();
    }

}
