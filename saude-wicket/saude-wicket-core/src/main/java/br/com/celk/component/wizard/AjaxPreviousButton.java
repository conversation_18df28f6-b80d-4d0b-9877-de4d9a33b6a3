package br.com.celk.component.wizard;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 */
public class AjaxPreviousButton extends AjaxWizardButton {

    public AjaxPreviousButton(final String id, final IAjaxWizard wizard) {
        super(id, wizard);
    }

    @Override
    public boolean isEnabled() {
        return getWizardModel().isPreviousAvailable();
    }

    @Override
    public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
        getWizardModel().previous(target);
        updateWizard(target);
    }

}
