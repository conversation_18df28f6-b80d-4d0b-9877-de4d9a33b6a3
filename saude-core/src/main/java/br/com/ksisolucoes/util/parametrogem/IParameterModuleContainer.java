/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.util.parametrogem;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface IParameterModuleContainer {

    public IParameterModuleContainer build() throws DAOException ;

    public <T> T getParametro(Long codigoEmpresa, Long codigoUsuario, String parametro);

    public <T> T getParametro(Long codigoEmpresa, Long codigoUsuario, Long codigoTipoProcedimento, String parametro);

    public <T> T getParametro(Long codigoEmpresa, Long codigoUsuario, List<TabelaCbo> cbosProfissional, Long codigoTipoProcedimento, String parametro);

    public <T> T getParametro(String parametro);

}
