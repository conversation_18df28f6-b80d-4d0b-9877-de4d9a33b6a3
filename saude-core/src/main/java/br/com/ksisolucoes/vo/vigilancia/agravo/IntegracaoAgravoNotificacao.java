package br.com.ksisolucoes.vo.vigilancia.agravo;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.vigilancia.agravo.base.BaseIntegracaoAgravoNotificacao;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class IntegracaoAgravoNotificacao extends BaseIntegracaoAgravoNotificacao implements CodigoManager {
	private static final long serialVersionUID = 1L;

	public enum TipoRequisicao implements IEnum {
		POST(0L, Bundle.getStringApplication("post")),
		PUT(1L, Bundle.getStringApplication("put")),
		DELETE(2L, Bundle.getStringApplication("delete")),;

		private final Long value;
		private final String descricao;

		TipoRequisicao(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static IntegracaoAgravoNotificacao.TipoRequisicao valeuOf(Long value) {
			for (IntegracaoAgravoNotificacao.TipoRequisicao status : IntegracaoAgravoNotificacao.TipoRequisicao.values()) {
				if (status.value().equals(value)) {
					return status;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}

	}


/*[CONSTRUCTOR MARKER BEGIN]*/
	public IntegracaoAgravoNotificacao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public IntegracaoAgravoNotificacao (br.com.ksisolucoes.vo.vigilancia.agravo.IntegracaoAgravoPK id) {
		super(id);
	}

	/**
	 * Constructor for required fields
	 */
	public IntegracaoAgravoNotificacao (
		br.com.ksisolucoes.vo.vigilancia.agravo.IntegracaoAgravoPK id,
		java.util.Date dataIntegracao) {

		super (
			id,
			dataIntegracao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setId( (br.com.ksisolucoes.vo.vigilancia.agravo.IntegracaoAgravoPK)key );
    }

    public Serializable getCodigoManager() {
        return this.getId();
    }
}