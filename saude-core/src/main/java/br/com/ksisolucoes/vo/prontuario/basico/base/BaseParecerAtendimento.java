package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the parecer_atendimento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="parecer_atendimento"
 */

public abstract class BaseParecerAtendimento extends BaseRootVO implements Serializable {

	public static String REF = "ParecerAtendimento";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_REGISTRO = "dataRegistro";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_DATA_HISTORICO = "dataHistorico";
	public static final String PROP_PARECER = "parecer";
	public static final String PROP_ATENDIMENTO = "atendimento";


	// constructors
	public BaseParecerAtendimento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseParecerAtendimento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseParecerAtendimento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento,
		java.lang.String parecer,
		java.util.Date dataHistorico,
		java.util.Date dataRegistro) {

		this.setCodigo(codigo);
		this.setAtendimento(atendimento);
		this.setParecer(parecer);
		this.setDataHistorico(dataHistorico);
		this.setDataRegistro(dataRegistro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String parecer;
	private java.util.Date dataHistorico;
	private java.util.Date dataRegistro;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_parecer"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: parecer
	 */
	public java.lang.String getParecer () {
		return getPropertyValue(this, parecer, PROP_PARECER); 
	}

	/**
	 * Set the value related to the column: parecer
	 * @param parecer the parecer value
	 */
	public void setParecer (java.lang.String parecer) {
//        java.lang.String parecerOld = this.parecer;
		this.parecer = parecer;
//        this.getPropertyChangeSupport().firePropertyChange ("parecer", parecerOld, parecer);
	}



	/**
	 * Return the value associated with the column: dt_historico
	 */
	public java.util.Date getDataHistorico () {
		return getPropertyValue(this, dataHistorico, PROP_DATA_HISTORICO); 
	}

	/**
	 * Set the value related to the column: dt_historico
	 * @param dataHistorico the dt_historico value
	 */
	public void setDataHistorico (java.util.Date dataHistorico) {
//        java.util.Date dataHistoricoOld = this.dataHistorico;
		this.dataHistorico = dataHistorico;
//        this.getPropertyChangeSupport().firePropertyChange ("dataHistorico", dataHistoricoOld, dataHistorico);
	}



	/**
	 * Return the value associated with the column: dt_registro
	 */
	public java.util.Date getDataRegistro () {
		return getPropertyValue(this, dataRegistro, PROP_DATA_REGISTRO); 
	}

	/**
	 * Set the value related to the column: dt_registro
	 * @param dataRegistro the dt_registro value
	 */
	public void setDataRegistro (java.util.Date dataRegistro) {
//        java.util.Date dataRegistroOld = this.dataRegistro;
		this.dataRegistro = dataRegistro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataRegistro", dataRegistroOld, dataRegistro);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.ParecerAtendimento)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.ParecerAtendimento parecerAtendimento = (br.com.ksisolucoes.vo.prontuario.basico.ParecerAtendimento) obj;
			if (null == this.getCodigo() || null == parecerAtendimento.getCodigo()) return false;
			else return (this.getCodigo().equals(parecerAtendimento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}