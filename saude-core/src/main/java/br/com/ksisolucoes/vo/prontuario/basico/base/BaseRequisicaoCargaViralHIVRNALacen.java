package br.com.ksisolucoes.vo.prontuario.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the requisicao_carga_viral_hiv_rna_lacen table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="requisicao_carga_viral_hiv_rna_lacen"
 */

public abstract class BaseRequisicaoCargaViralHIVRNALacen extends BaseRootVO implements Serializable {

	public static String REF = "RequisicaoCargaViralHIVRNALacen";
	public static final String PROP_MOTIVO_SOLICITACAO = "motivoSolicitacao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_SOLICITACAO_SIMULTANEA = "solicitacaoSimultanea";
	public static final String PROP_EXAME_REQUISICAO = "exameRequisicao";
	public static final String PROP_CID = "cid";


	// constructors
	public BaseRequisicaoCargaViralHIVRNALacen () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRequisicaoCargaViralHIVRNALacen (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRequisicaoCargaViralHIVRNALacen (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao,
		br.com.ksisolucoes.vo.prontuario.basico.Cid cid) {

		this.setCodigo(codigo);
		this.setExameRequisicao(exameRequisicao);
		this.setCid(cid);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long motivoSolicitacao;
	private java.lang.Long solicitacaoSimultanea;
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao;
	private br.com.ksisolucoes.vo.prontuario.basico.Cid cid;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_req_carga_viral_hiv_rna"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: motivo_solicitacao
	 */
	public java.lang.Long getMotivoSolicitacao () {
		return getPropertyValue(this, motivoSolicitacao, PROP_MOTIVO_SOLICITACAO); 
	}

	/**
	 * Set the value related to the column: motivo_solicitacao
	 * @param motivoSolicitacao the motivo_solicitacao value
	 */
	public void setMotivoSolicitacao (java.lang.Long motivoSolicitacao) {
//        java.lang.Long motivoSolicitacaoOld = this.motivoSolicitacao;
		this.motivoSolicitacao = motivoSolicitacao;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoSolicitacao", motivoSolicitacaoOld, motivoSolicitacao);
	}



	/**
	 * Return the value associated with the column: solicitacao_simultanea
	 */
	public java.lang.Long getSolicitacaoSimultanea () {
		return getPropertyValue(this, solicitacaoSimultanea, PROP_SOLICITACAO_SIMULTANEA); 
	}

	/**
	 * Set the value related to the column: solicitacao_simultanea
	 * @param solicitacaoSimultanea the solicitacao_simultanea value
	 */
	public void setSolicitacaoSimultanea (java.lang.Long solicitacaoSimultanea) {
//        java.lang.Long solicitacaoSimultaneaOld = this.solicitacaoSimultanea;
		this.solicitacaoSimultanea = solicitacaoSimultanea;
//        this.getPropertyChangeSupport().firePropertyChange ("solicitacaoSimultanea", solicitacaoSimultaneaOld, solicitacaoSimultanea);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cd_exame_requisicao
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao getExameRequisicao () {
		return getPropertyValue(this, exameRequisicao, PROP_EXAME_REQUISICAO); 
	}

	/**
	 * Set the value related to the column: cd_exame_requisicao
	 * @param exameRequisicao the cd_exame_requisicao value
	 */
	public void setExameRequisicao (br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicao) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao exameRequisicaoOld = this.exameRequisicao;
		this.exameRequisicao = exameRequisicao;
//        this.getPropertyChangeSupport().firePropertyChange ("exameRequisicao", exameRequisicaoOld, exameRequisicao);
	}



	/**
	 * Return the value associated with the column: cd_cid
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cid getCid () {
		return getPropertyValue(this, cid, PROP_CID); 
	}

	/**
	 * Set the value related to the column: cd_cid
	 * @param cid the cd_cid value
	 */
	public void setCid (br.com.ksisolucoes.vo.prontuario.basico.Cid cid) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cid cidOld = this.cid;
		this.cid = cid;
//        this.getPropertyChangeSupport().firePropertyChange ("cid", cidOld, cid);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.RequisicaoCargaViralHIVRNALacen)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.RequisicaoCargaViralHIVRNALacen requisicaoCargaViralHIVRNALacen = (br.com.ksisolucoes.vo.prontuario.basico.RequisicaoCargaViralHIVRNALacen) obj;
			if (null == this.getCodigo() || null == requisicaoCargaViralHIVRNALacen.getCodigo()) return false;
			else return (this.getCodigo().equals(requisicaoCargaViralHIVRNALacen.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}