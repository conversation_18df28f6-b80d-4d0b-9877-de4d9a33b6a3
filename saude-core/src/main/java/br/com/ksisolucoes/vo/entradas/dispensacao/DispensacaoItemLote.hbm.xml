<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.dispensacao"  >
    <class name="DispensacaoItemLote" table="dispensacao_item_lote">
        
        <id
            name="codigo"
            type="java.lang.Long" 
            column="cd_dispensacao_item_lote"
        > 
            <generator class="assigned"/>
        </id> 
        
        <version column="version" name="version" type="long" />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem"
            name="dispensacaoMedicamentoItem"
            column="cd_dis_med_item"
            not-null="true"
        />
            
        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque"
            name="grupoEstoque"
            not-null="true"
        >
            <column name="cod_pro" />
            <column name="empresa_grupo" />
            <column name="grupo_estoque" />
            <column name="cod_deposito" />
            <column name="cd_localizacao_estrutura" />
        </many-to-one>
        
        <property 
            name="quantidade"
            column="qtdade"
            type="java.lang.Double"
            not-null="true"
       	/>

        <property
            column="quantidade_devolvida"
            name="quantidadeDevolvida"
            not-null="false"
            type="java.lang.Double"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcessoEnvio"
                name="sincronizacaoHorusProcessoEnvio"
        >
            <column name="cd_horus_sincronizacao_proc_envio" />
        </many-to-one>
                
    </class>
</hibernate-mapping>
