package br.com.ksisolucoes.vo.atendimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the lancto_bpa_consolidado_item table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="lancto_bpa_consolidado_item"
 */

public abstract class BaseLancamentoBpaConsolidadoItem extends BaseRootVO implements Serializable {

	public static String REF = "LancamentoBpaConsolidadoItem";
	public static final String PROP_LANCAMENTO_BPA_CONSOLIDADO = "lancamentoBpaConsolidado";
	public static final String PROP_PROCEDIMENTO = "procedimento";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_IDADE = "idade";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_QUANTIDADE = "quantidade";


	// constructors
	public BaseLancamentoBpaConsolidadoItem () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseLancamentoBpaConsolidadoItem (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseLancamentoBpaConsolidadoItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.atendimento.LancamentoBpaConsolidado lancamentoBpaConsolidado,
		br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.Long quantidade,
		java.util.Date dataCadastro,
		java.lang.Long idade) {

		this.setCodigo(codigo);
		this.setLancamentoBpaConsolidado(lancamentoBpaConsolidado);
		this.setProcedimento(procedimento);
		this.setUsuario(usuario);
		this.setQuantidade(quantidade);
		this.setDataCadastro(dataCadastro);
		this.setIdade(idade);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long quantidade;
	private java.util.Date dataCadastro;
	private java.lang.Long idade;

	// many to one
	private br.com.ksisolucoes.vo.atendimento.LancamentoBpaConsolidado lancamentoBpaConsolidado;
	private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_lancto_bpa_consolidado_item"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: quantidade
	 */
	public java.lang.Long getQuantidade () {
		return getPropertyValue(this, quantidade, PROP_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: quantidade
	 * @param quantidade the quantidade value
	 */
	public void setQuantidade (java.lang.Long quantidade) {
//        java.lang.Long quantidadeOld = this.quantidade;
		this.quantidade = quantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidade", quantidadeOld, quantidade);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: idade
	 */
	public java.lang.Long getIdade () {
		return getPropertyValue(this, idade, PROP_IDADE); 
	}

	/**
	 * Set the value related to the column: idade
	 * @param idade the idade value
	 */
	public void setIdade (java.lang.Long idade) {
//        java.lang.Long idadeOld = this.idade;
		this.idade = idade;
//        this.getPropertyChangeSupport().firePropertyChange ("idade", idadeOld, idade);
	}



	/**
	 * Return the value associated with the column: cd_lancto_bpa_consolidado
	 */
	public br.com.ksisolucoes.vo.atendimento.LancamentoBpaConsolidado getLancamentoBpaConsolidado () {
		return getPropertyValue(this, lancamentoBpaConsolidado, PROP_LANCAMENTO_BPA_CONSOLIDADO); 
	}

	/**
	 * Set the value related to the column: cd_lancto_bpa_consolidado
	 * @param lancamentoBpaConsolidado the cd_lancto_bpa_consolidado value
	 */
	public void setLancamentoBpaConsolidado (br.com.ksisolucoes.vo.atendimento.LancamentoBpaConsolidado lancamentoBpaConsolidado) {
//        br.com.ksisolucoes.vo.atendimento.LancamentoBpaConsolidado lancamentoBpaConsolidadoOld = this.lancamentoBpaConsolidado;
		this.lancamentoBpaConsolidado = lancamentoBpaConsolidado;
//        this.getPropertyChangeSupport().firePropertyChange ("lancamentoBpaConsolidado", lancamentoBpaConsolidadoOld, lancamentoBpaConsolidado);
	}



	/**
	 * Return the value associated with the column: cd_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimento () {
		return getPropertyValue(this, procedimento, PROP_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_procedimento
	 * @param procedimento the cd_procedimento value
	 */
	public void setProcedimento (br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoOld = this.procedimento;
		this.procedimento = procedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimento", procedimentoOld, procedimento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.atendimento.LancamentoBpaConsolidadoItem)) return false;
		else {
			br.com.ksisolucoes.vo.atendimento.LancamentoBpaConsolidadoItem lancamentoBpaConsolidadoItem = (br.com.ksisolucoes.vo.atendimento.LancamentoBpaConsolidadoItem) obj;
			if (null == this.getCodigo() || null == lancamentoBpaConsolidadoItem.getCodigo()) return false;
			else return (this.getCodigo().equals(lancamentoBpaConsolidadoItem.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}