package br.com.ksisolucoes.vo.entradas.estoque.base;

import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


public abstract class BaseGrupoEstoquePK extends BaseRootVO implements Serializable {

	protected int hashCode = Integer.MIN_VALUE;

	public static String PROP_ESTOQUE_EMPRESA = "estoqueEmpresa";
	public static String PROP_GRUPO = "grupo";
	public static String PROP_CODIGO_DEPOSITO = "codigoDeposito";
	public static String PROP_LOCALIZACAO_ESTRUTURA = "localizacaoEstrutura";

	private br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa estoqueEmpresa;
	private java.lang.String grupo;
	private java.lang.Long codigoDeposito;
	private br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura localizacaoEstrutura;


	public BaseGrupoEstoquePK () {}
	
	public BaseGrupoEstoquePK (
		br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa estoqueEmpresa,
		java.lang.String grupo,
		java.lang.Long codigoDeposito,
		br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura localizacaoEstrutura) {

		this.setEstoqueEmpresa(estoqueEmpresa);
		this.setGrupo(grupo);
		this.setCodigoDeposito(codigoDeposito);
		this.setLocalizacaoEstrutura(localizacaoEstrutura);
	}


	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa getEstoqueEmpresa () {
		return getPropertyValue(this, estoqueEmpresa, PROP_ESTOQUE_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param estoqueEmpresa the empresa value
	 */
	public void setEstoqueEmpresa (br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa estoqueEmpresa) {
//        br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa estoqueEmpresaOld = this.estoqueEmpresa;
		this.estoqueEmpresa = estoqueEmpresa;
//        this.getPropertyChangeSupport().firePropertyChange ("estoqueEmpresa", estoqueEmpresaOld, estoqueEmpresa);
	}



	/**
	 * Return the value associated with the column: grupo_estoque
	 */
	public java.lang.String getGrupo () {
		return getPropertyValue(this, grupo, PROP_GRUPO); 
	}

	/**
	 * Set the value related to the column: grupo_estoque
	 * @param grupo the grupo_estoque value
	 */
	public void setGrupo (java.lang.String grupo) {
		this.grupo = grupo;
	}



	/**
	 * Return the value associated with the column: cod_deposito
	 */
	public java.lang.Long getCodigoDeposito () {
		return getPropertyValue(this, codigoDeposito, PROP_CODIGO_DEPOSITO); 
	}

	/**
	 * Set the value related to the column: cod_deposito
	 * @param codigoDeposito the cod_deposito value
	 */
	public void setCodigoDeposito (java.lang.Long codigoDeposito) {
//        java.lang.Long codigoDepositoOld = this.codigoDeposito;
		this.codigoDeposito = codigoDeposito;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoDeposito", codigoDepositoOld, codigoDeposito);
	}



	/**
	 * Return the value associated with the column: cd_localizacao_estrutura
	 */
	public br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura getLocalizacaoEstrutura () {
		return getPropertyValue(this, localizacaoEstrutura, PROP_LOCALIZACAO_ESTRUTURA); 
	}

	/**
	 * Set the value related to the column: cd_localizacao_estrutura
	 * @param localizacaoEstrutura the cd_localizacao_estrutura value
	 */
	public void setLocalizacaoEstrutura (br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura localizacaoEstrutura) {
//        br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura localizacaoEstruturaOld = this.localizacaoEstrutura;
		this.localizacaoEstrutura = localizacaoEstrutura;
//        this.getPropertyChangeSupport().firePropertyChange ("localizacaoEstrutura", localizacaoEstruturaOld, localizacaoEstrutura);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoquePK)) return false;
		else {
			br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoquePK mObj = (br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoquePK) obj;
			if (null != this.getEstoqueEmpresa() && null != mObj.getEstoqueEmpresa()) {
				if (!this.getEstoqueEmpresa().equals(mObj.getEstoqueEmpresa())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getGrupo() && null != mObj.getGrupo()) {
				if (!this.getGrupo().equals(mObj.getGrupo())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getCodigoDeposito() && null != mObj.getCodigoDeposito()) {
				if (!this.getCodigoDeposito().equals(mObj.getCodigoDeposito())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getLocalizacaoEstrutura() && null != mObj.getLocalizacaoEstrutura()) {
				if (!this.getLocalizacaoEstrutura().equals(mObj.getLocalizacaoEstrutura())) {
					return false;
				}
			}
			else {
				return false;
			}
			return true;
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			StringBuilder sb = new StringBuilder();
			if (null != this.getEstoqueEmpresa()) {
				sb.append(this.getEstoqueEmpresa().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getGrupo()) {
				sb.append(this.getGrupo().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getCodigoDeposito()) {
				sb.append(this.getCodigoDeposito().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getLocalizacaoEstrutura()) {
				sb.append(this.getLocalizacaoEstrutura().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			this.hashCode = sb.toString().hashCode();
		}
		return this.hashCode;
	}

    private java.beans.PropertyChangeSupport propertyChangeSupport;

    protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
        if( this.propertyChangeSupport == null ) {
            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
        }
        return this.propertyChangeSupport;
    }

    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.addPropertyChangeListener(l);
    }

    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
		propertyChangeSupport.addPropertyChangeListener(propertyName, listener);
    }

    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.removePropertyChangeListener(l);
    }
}