package br.com.ksisolucoes.vo.hospital.ipe;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;

import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.vo.hospital.ipe.base.BaseHonorariosIpe;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;



public class HonorariosIpe extends BaseHonorariosIpe implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;
        public static final String PROP_DESCRICAO_TIPO_MOEDA = "descricaoTipoMoeda";
        public static final String PROP_DESCRICAO_TIPO_HONORARIO = "descricaoTipoHonorario";
        
        public enum TipoHonorario implements IEnum {
            CIRURGICO(0L, Bundle.getStringApplication("rotulo_cirurgico")),
            OBSTETRICO(1L, Bundle.getStringApplication("rotulo_obstetrico")),
            CLINICO(2L, Bundle.getStringApplication("rotulo_clinico")),
            ANESTESICO(3L, Bundle.getStringApplication("rotulo_anestesico")),
            DIAGNOSE(4L, Bundle.getStringApplication("rotulo_diagnose")),
            TERAPIA(5L, Bundle.getStringApplication("rotulo_terapia")),
            OUTROS(6L, Bundle.getStringApplication("rotulo_outros")),
            PRONTO_ATENDIMENTO(7L, Bundle.getStringApplication("rotulo_pronto_atendimento"));

            private Long value;
            private String descricao;

            private TipoHonorario(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static TipoHonorario valeuOf(Long value) {
                for (TipoHonorario tipoHonorario : TipoHonorario.values()) {
                    if (tipoHonorario.value().equals(value)) {
                        return tipoHonorario;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }
        
        public enum TipoMoeda implements IEnum {
            REAL(1L, Bundle.getStringApplication("rotulo_reais_maiusculo")),
            UPS(2L, Bundle.getStringApplication("rotulo_ups_maiusculo"));

            private Long value;
            private String descricao;

            private TipoMoeda(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

            public static TipoMoeda valeuOf(Long value) {
                for (TipoMoeda tipoMoeda : TipoMoeda.values()) {
                    if (tipoMoeda.value().equals(value)) {
                        return tipoMoeda;
                    }
                }
                return null;
            }

            @Override
            public String toString() {
                return this.descricao;
            }
        }

/*[CONSTRUCTOR MARKER BEGIN]*/
	public HonorariosIpe () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public HonorariosIpe (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public HonorariosIpe (
		java.lang.Long codigo,
		java.util.Date dataInicioValidade,
		java.lang.Double valor,
		java.lang.Long situacao,
		java.lang.Long tipoHonorario) {

		super (
			codigo,
			dataInicioValidade,
			valor,
			situacao,
			tipoHonorario);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return Util.getDescricaoFormatado(this.getProcedimento().getReferencia(), this.getProcedimento().getDescricao());
    }

    @Override
    public String getIdentificador() {
        return this.getCodigo().toString();
    }

    public String getDescricaoTipoMoeda(){
        TipoMoeda tipoMoeda = TipoMoeda.valeuOf(getTipoMoeda());
        if (tipoMoeda != null && tipoMoeda.descricao != null) {
            return tipoMoeda.descricao();
        }
        return "";
    }
    
    public String getDescricaoTipoHonorario(){
        TipoHonorario tipoHonorario = TipoHonorario.valeuOf(getTipoHonorario());
        if (tipoHonorario != null && tipoHonorario.descricao != null) {
            return tipoHonorario.descricao();
        }
        return "";
    }
}