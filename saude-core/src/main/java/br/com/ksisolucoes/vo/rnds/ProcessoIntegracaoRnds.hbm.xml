<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.rnds">
    <class name="ProcessoIntegracaoRnds" table="processo_integracao_rnds">
        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_processo_integracao_rnds"
        >
            <generator class="sequence">
                <param name="sequence">seq_processo_integracao_rnds</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <many-to-one class="br.com.ksisolucoes.vo.controle.Usuario" name="usuario">
            <column name="cd_usuario"/>
        </many-to-one>

        <property
                column="ds_erro"
                name="descricaoErro"
                type="java.lang.String"
        />

        <property
                name="status"
                column="status"
                type="java.lang.Long"
                not-null="true"
        />
        <property
                name="tipoProcessamento"
                column="tp_processamento"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="dt_processo"
                name="dataProcesso"
                type="timestamp"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.service.AsyncProcess"
                column="cd_process"
                name="asyncProcess"
                not-null="true"
        />

        <property
                name="periodo"
                column="periodo"
                type="java.lang.String"
        />

        <property
                column="ds_unidade"
                name="descricaoUnidade"
                type="java.lang.String"
        />

    </class>
</hibernate-mapping>
