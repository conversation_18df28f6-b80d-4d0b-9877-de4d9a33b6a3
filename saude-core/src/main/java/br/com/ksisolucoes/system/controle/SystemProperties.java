package br.com.ksisolucoes.system.controle;

import br.com.ksisolucoes.util.PropertiesServerInterface;

public class SystemProperties implements PropertiesServerInterface {

    private static SystemProperties instance;
    
    private String propertieDevelopmentSession;
    private Boolean testSession;
    private String serverUrl;
    private String helpFileJar;
    private String helpFileHS;
    private String screenSizeWidth;
    private String screenSizeHeight;
    private String pathViewJrxml;
    private String applicationHome;
    private String jdbcConnectionURL;
    private String jdbcDriverClasse;
    private String jdbcUsername;
    private String jdbcPassword;
    
    private SystemProperties() {
    }

    /**
     * Retorna o valor da propriedade de sistema development.session
     * 
     * @return Valor de retorno default caso no exista: "false"
     */
    public Boolean isTestSession() {
        if( this.testSession == null ) {
            // TODO - marcelo colocar constante no ksi-commons
            this.testSession = Boolean.getBoolean("test.session");
        }
        
        return this.testSession;
    }
    
    /**
     * Retorna o valor da propriedade de sistema development.session
     * 
     * @return Valor de retorno default caso no exista: "false"
     */
    public String getPropertieDevelopmentSession() {
        if( this.propertieDevelopmentSession == null ) {
            this.propertieDevelopmentSession = System.getProperty( PROPERTIE_DEVELOPMENT_SESSION, "false" );
        }
        
        return this.propertieDevelopmentSession;
    }
    
    public String getScreenSizeWidth() {
        if( this.screenSizeWidth == null ) {
            this.screenSizeWidth = System.getProperty( "screenSizeWidth", null );
        }
        
        return this.screenSizeWidth;
    }
    
    public String getScreenSizeHeight() {
        if( this.screenSizeHeight == null ) {
            this.screenSizeHeight = System.getProperty( "screenSizeHeight", null );
        }
        
        return this.screenSizeHeight;
    }
    
    public String getPatchViewJrxml() {
        if( this.pathViewJrxml == null ) {
            this.pathViewJrxml = System.getProperty( "ireport.home.exec", null );
        }
        
        return this.pathViewJrxml;
    }
    
    public String getApplicationHome() {
        if( this.applicationHome == null ) {
            this.applicationHome = System.getProperty("app.home", null);
        }
        
        return this.applicationHome;
    }
    
    /**
     * Retorna o valor da propriedade de sistema development.session
     * 
     * @return Valor de retorno default caso no exista: "false"
     */
    public String getHelpFileJar() {
        if( this.helpFileJar == null ) {
            this.helpFileJar = System.getProperty( "help.file.jar", "" );
        }
        
        return this.helpFileJar;
    }
    
    /**
     * Retorna o valor da propriedade de sistema development.session
     * 
     * @return Valor de retorno default caso no exista: "false"
     */
    public String getHelpFileHS() {
        if( this.helpFileHS == null ) {
            this.helpFileHS = System.getProperty( "help.file.hs", "" );
        }
        
        return this.helpFileHS;
    }

    public static SystemProperties getInstance() {
        if( instance == null ) {
            instance = new SystemProperties();
        }
        
        return instance;
    }

    public String getJdbcConnectionURL() {
        if (this.jdbcConnectionURL == null) {
            this.jdbcConnectionURL = System.getProperty("jdbc.connection.url");
        }
        return this.jdbcConnectionURL;
    }

    public String getJdbcDriverClasse() {
        if (this.jdbcDriverClasse == null) {
            this.jdbcDriverClasse = System.getProperty("jdbc.driver.classe");
        }
        return this.jdbcDriverClasse;
    }

    public String getJdbcPassword() {
        if (this.jdbcPassword == null) {
            this.jdbcPassword = System.getProperty("jdbc.password");
        }
        return jdbcPassword;
    }

    public String getJdbcUsername() {
        if (this.jdbcUsername == null) {
            this.jdbcUsername = System.getProperty("jdbc.username");
        }
        return jdbcUsername;
    }

}
