package br.com.ksisolucoes.bo.dto;

import br.com.ksisolucoes.vo.prontuario.basico.AnexoPaciente;
import br.com.ksisolucoes.vo.prontuario.basico.AnexoPacienteElo;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAnexo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AnexosPacienteDTO implements Serializable {

    private AnexoPaciente anexoPaciente;
    private List<AnexoPacienteElo> elos = new ArrayList();
    private List<MensagemAnexoDTO> anexos = new ArrayList();
    private TipoAnexo tipoAnexo;

    public AnexoPaciente getAnexoPaciente() {
        return anexoPaciente;
    }

    public void setAnexoPaciente(AnexoPaciente anexoPaciente) {
        this.anexoPaciente = anexoPaciente;
    }

    public List<AnexoPacienteElo> getElos() {
        return elos;
    }

    public void setElos(List<AnexoPacienteElo> elos) {
        this.elos = elos;
    }

    public List<MensagemAnexoDTO> getAnexos() {
        return anexos;
    }

    public void setAnexos(List<MensagemAnexoDTO> anexos) {
        this.anexos = anexos;
    }

    public void addAnexo(MensagemAnexoDTO dto) {
        this.anexos.add(dto);
    }

    public TipoAnexo getTipoAnexo() {
        return tipoAnexo;
    }

    public void setTipoAnexo(TipoAnexo tipoAnexo) {
        this.tipoAnexo = tipoAnexo;
    }
}
