SET application_name = 'flyway|3.0.68';

/*
    Roger - 15/05/2017 - #14962
*/
ALTER TABLE requerimento_alvara ADD COLUMN dt_validade_provisoria TIMESTAMP NULL;
ALTER TABLE auditschema.requerimento_alvara ADD COLUMN dt_validade_provisoria TIMESTAMP NULL;

ALTER TABLE configuracao_vigilancia ADD COLUMN flag_permite_alvara_provisorio SMALLINT NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN flag_permite_alvara_provisorio SMALLINT NULL;

INSERT INTO agendador_processo (cd_agendador_processo, cd_processo, nm_servico, ds_servico, status, version, tp_processo)
VALUES (39, 39, 'Notificação de Alvarás Provisórios Vencidos', 'Executa o processo de notificação de alvarás provisórios vencidos.', 0, 0, 12);

/*
    Roger - 17/05/2017 - #14983
*/
ALTER TABLE vigilancia_financeiro ADD COLUMN ds_motivo VARCHAR(500) NULL;
ALTER TABLE auditschema.vigilancia_financeiro ADD COLUMN ds_motivo VARCHAR(500) NULL;


/*
    Laudecir - 17/05/2017 - #14956
*/
CREATE TABLE substancia (
	cd_substancia   INT8        NOT NULL,
	descricao	    VARCHAR     NOT NULL,
	version         INT8        NOT NULL,
	CONSTRAINT pk_substancia PRIMARY KEY (cd_substancia)
);

CREATE TABLE auditschema.substancia AS SELECT t2.*, t1.* FROM substancia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_substancia;alter table auditschema.substancia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON substancia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE TABLE estabelecimento_substancias (
    cd_estab_substancia     INT8    NOT NULL,
    cd_estabelecimento      INT8    NOT NULL,
    cd_substancia           INT8    NOT NULL,
    version                 INT8    NOT NULL,
    CONSTRAINT pk_estab_subst PRIMARY KEY (cd_estab_substancia),
    CONSTRAINT fk_estabelecimento_ref_estab_subst FOREIGN KEY (cd_estabelecimento)
        REFERENCES estabelecimento (cd_estabelecimento) ON UPDATE RESTRICT ON DELETE RESTRICT,
    CONSTRAINT fk_substancia_ref_estab_subst FOREIGN KEY (cd_substancia)
        REFERENCES substancia (cd_substancia) ON UPDATE RESTRICT ON DELETE RESTRICT
);

CREATE TABLE auditschema.estabelecimento_substancias AS SELECT t2.*, t1.* FROM estabelecimento_substancias t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_estabelecimento_substancias;alter table auditschema.estabelecimento_substancias add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON estabelecimento_substancias FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE UNIQUE index idx_estabelecimento_substancia on estabelecimento_substancias (
    cd_estabelecimento,
    cd_substancia
);

alter table estabelecimento add column flag_trabalha_medicamento_controlado int2;
alter table auditschema.estabelecimento add column flag_trabalha_medicamento_controlado int2;

/*
    Maicon - 18/05/2017 - #14998
*/
ALTER TABLE configuracao_vigilancia ADD COLUMN flag_permite_impressao_externa INT2  NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN flag_permite_impressao_externa INT2  NULL;

/*
    Roger - 18/05/2017 - #14993
*/
ALTER TABLE requerimento_vigilancia_anexo ADD COLUMN observacao VARCHAR(500) NULL;
ALTER TABLE auditschema.requerimento_vigilancia_anexo ADD COLUMN observacao VARCHAR(500) NULL;

/*
    Laudecir - 19/05/2017 - #14979
*/
ALTER TABLE configuracao_vigilancia ADD COLUMN flag_emitir_termo_solic_servico INT2  NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN flag_emitir_termo_solic_servico INT2  NULL;

/*
    Maicon - 19/05/2017 - #15001
*/
ALTER TABLE configuracao_vigilancia ADD COLUMN cd_taxa_alvara_evento INT8 NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN cd_taxa_alvara_evento INT8 NULL;

ALTER TABLE configuracao_vigilancia ADD COLUMN qtd_taxa_alvara_evento numeric(12,4) NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN qtd_taxa_alvara_evento numeric(12,4) NULL;

alter table configuracao_vigilancia
   add constraint FK_CONFIG_VIG_REF_TAXA_ALVARA_EVENTO foreign key (cd_taxa_alvara_evento)
      references taxa (cd_taxa)
      on delete restrict on update restrict;

ALTER TABLE requerimento_vigilancia_anexo ADD COLUMN visualizado SMALLINT NULL;
ALTER TABLE auditschema.requerimento_vigilancia_anexo ADD COLUMN visualizado SMALLINT NULL;

/*
    Maicon - 24/05/2017 - #15019
*/
CREATE TABLE motivo_retorno (
	cd_motivo_retorno   INT8        NOT NULL,
	descricao	        VARCHAR     NOT NULL,
	cd_usuario          NUMERIC(6)  not null,
    dt_cadastro         TIMESTAMP   not null,
	version             INT8        NOT NULL,
	CONSTRAINT pk_motivo_retorno PRIMARY KEY (cd_motivo_retorno)
);

CREATE TABLE auditschema.motivo_retorno AS SELECT t2.*, t1.* FROM motivo_retorno t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_motivo_retorno;alter table auditschema.motivo_retorno add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON motivo_retorno FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

ALTER TABLE auto_intimacao ADD COLUMN enviado_ar INT2 NULL;
ALTER TABLE auditschema.auto_intimacao ADD COLUMN enviado_ar INT2 NULL;

alter table  auto_intimacao_exigencia alter column dt_cumprimento_prazo drop not null;
/*
    Sulivan - 23/05/2017 - #15020
*/
INSERT INTO programa_pagina VALUES (1399, 'br.com.celk.view.vigilancia.requerimentovigilancia.PendenciasFiscalPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (762, 'Pendências Fiscais', 1399, 'N');
INSERT INTO programa_web_pagina VALUES (1439, 762, 1399);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1003,'Pendências Fiscais','pendenciasFiscais',836,762,307,0,0);

INSERT INTO programa_pagina VALUES (1400, 'br.com.celk.view.vigilancia.requerimentovigilancia.PendenciasRecepcaoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (763, 'Pendências Recepção', 1400, 'N');
INSERT INTO programa_web_pagina VALUES (1440, 763, 1400);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1004,'Pendências Recepção','pendenciasRecepcao',836,763,307,0,0);
/*
    Laudecir - 22/05/2017 - #15017
*/
CREATE TABLE requerimento_baixa_estabelecimento (
  cd_req_baixa_estabelecimento	int8		NOT NULL,
  cd_req_vigilancia				int8		NOT NULL,
  version						int8		NOT NULL,
  CONSTRAINT pk_req_baixa_estab PRIMARY KEY (cd_req_baixa_estabelecimento),
  CONSTRAINT fk_req_baix_estab_ref_req_vig FOREIGN KEY (cd_req_vigilancia)
        REFERENCES requerimento_vigilancia (cd_req_vigilancia) MATCH SIMPLE ON UPDATE RESTRICT ON DELETE RESTRICT
);

CREATE TABLE auditschema.requerimento_baixa_estabelecimento AS SELECT t2.*, t1.* FROM requerimento_baixa_estabelecimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_baixa_estabelecimento;alter table auditschema.requerimento_baixa_estabelecimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_baixa_estabelecimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

INSERT INTO programa_pagina VALUES (1398, 'br.com.celk.view.vigilancia.requerimentos.RequerimentoBaixaEstabelecimentoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1437, 684, 1398);
INSERT INTO programa_web_pagina VALUES (1438, 245, 1398);

INSERT INTO permissao_web (cd_permissao, ds_permissao, version) VALUES (103, 'Baixa Estabelecimento', 0);
INSERT INTO programa_pagina_permissao VALUES(324, 103, 1210, 0, 'baixaEstabelecimento');

INSERT INTO tipo_solicitacao (cd_tipo_solicitacao,ds_tipo_solicitacao,tipo_documento,tipo_acao,version,cd_templ_doc_vig,lei,registrar_palestra,valor_ufm,tp_requerimento,ativo) VALUES (nextval('seq_gem'),'Baixa de Estabelecimento',null,null,0,null,null,null,null,16,1);
/*
    Roger - 21/05/2017 - #14991
*/
ALTER TABLE requerimento_vigilancia_anexo ADD COLUMN cd_requerimento_licenca_transporte_veiculo BIGINT NULL;
ALTER TABLE auditschema.requerimento_vigilancia_anexo ADD COLUMN cd_requerimento_licenca_transporte_veiculo BIGINT NULL;
ALTER TABLE requerimento_vigilancia_anexo ALTER COLUMN cd_req_vigilancia DROP NOT NULL;
ALTER TABLE auditschema.requerimento_vigilancia_anexo ALTER COLUMN cd_req_vigilancia DROP NOT NULL;

ALTER TABLE requerimento_vigilancia_anexo ADD CONSTRAINT fk_req_vig_anexo_ref_req_lic_trans_veic FOREIGN KEY (cd_requerimento_licenca_transporte_veiculo)
        REFERENCES requerimento_licenca_transporte_veiculo (cd_requerimento_licenca_transporte_veiculo) ON UPDATE RESTRICT ON DELETE RESTRICT;

ALTER TABLE requerimento_licenca_transporte ALTER COLUMN cd_veiculo DROP NOT NULL;
ALTER TABLE auditschema.requerimento_licenca_transporte ALTER COLUMN cd_veiculo DROP NOT NULL;
ALTER TABLE requerimento_licenca_transporte_veiculo ALTER COLUMN protocolo DROP NOT NULL;
ALTER TABLE auditschema.requerimento_licenca_transporte_veiculo ALTER COLUMN protocolo DROP NOT NULL;

ALTER TABLE configuracao_vigilancia ADD COLUMN flag_taxa_unica_licenca_transporte SMALLINT NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN flag_taxa_unica_licenca_transporte SMALLINT NULL;
ALTER TABLE configuracao_vigilancia ADD COLUMN taxa_unica_licenca_transporte NUMERIC(12) NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN taxa_unica_licenca_transporte NUMERIC(12) NULL;

INSERT INTO programa_pagina VALUES (1401, 'br.com.celk.view.vigilancia.requerimentos.DeferimentoLicencaVeiculo  sPage', 'N');
INSERT INTO programa_web_pagina VALUES (1441, 245, 1401);

ALTER TABLE requerimento_licenca_transporte_veiculo ADD COLUMN status SMALLINT NOT NULL DEFAULT 0;
ALTER TABLE auditschema.requerimento_licenca_transporte_veiculo ADD COLUMN status SMALLINT NOT NULL DEFAULT 0;

ALTER TABLE requerimento_licenca_transporte ALTER COLUMN nro_licenca DROP NOT NULL;
ALTER TABLE auditschema.requerimento_licenca_transporte ALTER COLUMN nro_licenca DROP NOT NULL;

/*
    Roger - 26/05/2017 - #15039
*/
create table requerimento_baixa_veiculo (
    cd_requerimento_baixa_veiculo        INT8            not null,
    cd_req_vigilancia       	           INT8            not null,
    version                              INT8            not null,
    constraint PK_REQ_BAIXA_VEICULO primary key (cd_requerimento_baixa_veiculo),
    constraint FK_REQ_BAIXA_VEIC_REF_REQ_VIGI foreign key (cd_req_vigilancia)
      references requerimento_vigilancia (cd_req_vigilancia)
);
CREATE TABLE auditschema.requerimento_baixa_veiculo AS SELECT t2.*, t1.* FROM requerimento_baixa_veiculo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_baixa_veiculo;alter table auditschema.requerimento_baixa_veiculo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_baixa_veiculo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create table requerimento_baixa_veiculo_itens (
    cd_requerimento_baixa_veiculo_itens  INT8            not null,
    cd_requerimento_baixa_veiculo        INT8            not null,
    cd_veiculo                           INT8            not null,
    version                              INT8            not null,
    constraint PK_REQ_BAIXA_VEICULO_ITENS primary key (cd_requerimento_baixa_veiculo_itens),
    constraint FK_REQ_BAIXA_VEIC_ITENS_REF_REQ_BAIXA_VEIC foreign key (cd_requerimento_baixa_veiculo)
      references requerimento_baixa_veiculo (cd_requerimento_baixa_veiculo),
    constraint FK_REQ_BAIXA_VEIC_REF_VEI_ESTAB foreign key (cd_veiculo)
      references veiculo_estabelecimento (cd_veiculo)
);
CREATE TABLE auditschema.requerimento_baixa_veiculo_itens AS SELECT t2.*, t1.* FROM requerimento_baixa_veiculo_itens t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_baixa_veiculo_itens;alter table auditschema.requerimento_baixa_veiculo_itens add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_baixa_veiculo_itens FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

INSERT INTO programa_pagina VALUES (1402, 'br.com.celk.view.vigilancia.requerimentos.baixaveiculo.RequerimentoBaixaVeiculoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1442, 684, 1402);
INSERT INTO programa_web_pagina VALUES (1443, 245, 1402);

INSERT INTO permissao_web (cd_permissao, ds_permissao, version) VALUES (104, 'Baixa de Veículo', 0);
INSERT INTO programa_pagina_permissao (cd_prog_pag_perm,cd_permissao,cd_prg_pagina,version,ds_bundle) VALUES (325,104,1402,0,null);
INSERT INTO tipo_solicitacao (cd_tipo_solicitacao,ds_tipo_solicitacao,tipo_documento,tipo_acao,version,cd_templ_doc_vig,lei,registrar_palestra,valor_ufm,tp_requerimento,ativo) VALUES (nextval('seq_gem'),'Baixa de Veículos',null,null,0,null,null,null,null,17,1);

/*
    Laudecir - 29/05/2017 - #15048
*/
update permissao_web set ds_permissao = 'Inspeção Sanitária' where cd_permissao = 75;
update tipo_solicitacao set ds_tipo_solicitacao = 'Inspeção Sanitária' where tp_requerimento = 9;
alter table requerimento_inspecao_sanitaria add column motivo varchar;
alter table auditschema.requerimento_inspecao_sanitaria add column motivo varchar;

/*
    Laudecir - 29/05/2017 - #15055
*/
INSERT INTO programa_pagina VALUES (1404, 'br.com.celk.view.vigilancia.externo.view.servicos.RequerimentoBaixaEstabelecimentoExternoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1445, 692, 1404);
INSERT INTO programa_pagina_permissao (cd_prog_pag_perm,cd_permissao,cd_prg_pagina,version,ds_bundle) VALUES (326, 103, 1404, 0, null);

/*
    Laudecir - 29/05/2017 - #15056
*/
INSERT INTO permissao_web (cd_permissao, ds_permissao, version) VALUES (105, 'Inspeção Sanitária Comum', 0);
INSERT INTO permissao_web (cd_permissao, ds_permissao, version) VALUES (106, 'Inspeção Sanitária AFE/ANVISA/AE', 0);

/*
    Sulivan - 31/05/2017 - #15030
*/
INSERT INTO programa_pagina VALUES (1403, 'br.com.celk.view.vigilancia.requerimentovigilancia.ConsultaRequerimentoVigilanciaFiscalPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (764, 'Consulta dos Requerimentos/Protocolo Fiscal', 1403, 'N');
INSERT INTO programa_web_pagina VALUES (1444, 764, 1403);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1005,'Consulta dos Requerimentos/Protocolo Fiscal','consultaRequerimentosProtocoloFiscal',836,764,307,0,0);

ALTER TABLE tipo_solicitacao ADD COLUMN flag_inspeciona INT2 NULL;
ALTER TABLE auditschema.tipo_solicitacao ADD COLUMN flag_inspeciona INT2 NULL;
update tipo_solicitacao set flag_inspeciona = 0;
ALTER TABLE tipo_solicitacao ALTER COLUMN flag_inspeciona SET NOT NULL;

INSERT INTO programa_web_pagina VALUES (1446, 764, 1211);
INSERT INTO programa_web_pagina VALUES (1447, 764, 1218);
INSERT INTO programa_web_pagina VALUES (1448, 764, 1219);
INSERT INTO programa_web_pagina VALUES (1449, 764, 1220);
INSERT INTO programa_web_pagina VALUES (1450, 764, 1221);
INSERT INTO programa_web_pagina VALUES (1451, 764, 1223);
INSERT INTO programa_web_pagina VALUES (1452, 764, 1224);
INSERT INTO programa_web_pagina VALUES (1453, 764, 1225);
INSERT INTO programa_web_pagina VALUES (1454, 764, 1226);
INSERT INTO programa_web_pagina VALUES (1455, 764, 1227);
INSERT INTO programa_web_pagina VALUES (1456, 764, 1228);
INSERT INTO programa_web_pagina VALUES (1457, 764, 1229);
INSERT INTO programa_web_pagina VALUES (1458, 764, 1230);
INSERT INTO programa_web_pagina VALUES (1459, 764, 1231);
INSERT INTO programa_web_pagina VALUES (1460, 764, 1232);
INSERT INTO programa_web_pagina VALUES (1461, 764, 1237);
INSERT INTO programa_web_pagina VALUES (1462, 764, 1238);
INSERT INTO programa_web_pagina VALUES (1463, 764, 1239);
INSERT INTO programa_web_pagina VALUES (1464, 764, 1240);
INSERT INTO programa_web_pagina VALUES (1465, 764, 1241);
INSERT INTO programa_web_pagina VALUES (1466, 764, 1243);
INSERT INTO programa_web_pagina VALUES (1467, 764, 1247);
INSERT INTO programa_web_pagina VALUES (1468, 764, 1368);
INSERT INTO programa_web_pagina VALUES (1469, 764, 1370);
INSERT INTO programa_web_pagina VALUES (1470, 764, 1372);
INSERT INTO programa_web_pagina VALUES (1471, 764, 1381);
INSERT INTO programa_web_pagina VALUES (1472, 764, 1382);
INSERT INTO programa_web_pagina VALUES (1473, 764, 1384);
INSERT INTO programa_web_pagina VALUES (1474, 764, 1388);
INSERT INTO programa_web_pagina VALUES (1475, 764, 1393);
INSERT INTO programa_web_pagina VALUES (1476, 764, 1395);
INSERT INTO programa_web_pagina VALUES (1477, 764, 1396);
INSERT INTO programa_web_pagina VALUES (1478, 764, 1398);
INSERT INTO programa_web_pagina VALUES (1480, 764, 1401);

INSERT INTO programa_pagina_permissao VALUES (327, 2, 1403, 0, 'finalizar');
INSERT INTO programa_pagina_permissao VALUES (328, 3, 1403, 0, '');
INSERT INTO programa_pagina_permissao VALUES (329, 6, 1403, 0, '');
INSERT INTO programa_pagina_permissao VALUES (330, 7, 1403, 0, 'emAnalise');
INSERT INTO programa_pagina_permissao VALUES (331, 12, 1403, 0, '');
INSERT INTO programa_pagina_permissao VALUES (332, 19, 1403, 0, 'lancarOcorrencia');
INSERT INTO programa_pagina_permissao VALUES (333, 38, 1403, 0, 'entregaDocumento');
INSERT INTO programa_pagina_permissao VALUES (334, 98, 1403, 0, 'visualizarTodosRequerimentos');
INSERT INTO programa_pagina_permissao VALUES (335, 102, 1403, 0, 'conformidadeTecnica');

create table requerimento_vigilancia_fiscal (
	cd_req_vig_fiscal           INT8          not null,
    cd_req_vigilancia           INT8          not null,
    cd_profissional         	INT8          not null,
    dt_cadastro                 TIMESTAMP     not null,
    cd_usuario                  NUMERIC(6)    not null,
    version                     INT8          not null
);

alter table requerimento_vigilancia_fiscal
   add constraint PK_REQ_VIG_FISCAL primary key (cd_req_vig_fiscal);

alter table requerimento_vigilancia_fiscal
   add constraint FK_REQ_VIG_FISCAL_REF_REQ_VIG foreign key (cd_req_vigilancia)
      references requerimento_vigilancia (cd_req_vigilancia)
      on delete restrict on update restrict;

alter table requerimento_vigilancia_fiscal
   add constraint FK_REQ_VIG_FISCAL_REF_PROF foreign key (cd_profissional)
      references profissional (cd_profissional)
      on delete restrict on update restrict;

alter table requerimento_vigilancia_fiscal
   add constraint FK_REQ_VIG_FISCAL_REF_USU foreign key (cd_usuario)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

CREATE TABLE auditschema.requerimento_vigilancia_fiscal AS SELECT t2.*, t1.* FROM requerimento_vigilancia_fiscal t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_requerimento_vigilancia_fiscal;alter table auditschema.requerimento_vigilancia_fiscal add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON requerimento_vigilancia_fiscal FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

ALTER TABLE requerimento_vigilancia ADD COLUMN dt_inspecao DATE NULL;
ALTER TABLE auditschema.requerimento_vigilancia ADD COLUMN dt_inspecao DATE NULL;

/*
    Roger - 30/05/2017 - #14991
*/
ALTER TABLE configuracao_vigilancia ADD COLUMN cd_taxa_indice_unica_licenca_transporte INT8 NULL;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN cd_taxa_indice_unica_licenca_transporte INT8 NULL;

alter table configuracao_vigilancia
   add constraint FK_CONFIG_VIG_TAX_UNIC_LIC_TRANSP_REF_TAXA foreign key (cd_taxa_indice_unica_licenca_transporte)
      references taxa (cd_taxa)
      on delete restrict on update restrict;

/*
    Roger - 30/05/2017 - #15040
*/
INSERT INTO programa_pagina VALUES (1405, 'br.com.celk.view.vigilancia.externo.view.servicos.RequerimentoBaixaVeiculoExternoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1481, 692, 1405);
INSERT INTO programa_pagina_permissao (cd_prog_pag_perm,cd_permissao,cd_prg_pagina,version,ds_bundle) VALUES (336, 104, 1405, 0, null);

/*
    Roger - 30/05/2017 - #15071
*/
ALTER TABLE requerimento_licenca_transporte ADD COLUMN tp_licenca SMALLINT NOT NULL DEFAULT 0;
ALTER TABLE auditschema.requerimento_licenca_transporte ADD COLUMN tp_licenca SMALLINT NOT NULL DEFAULT 0;

/*
    Roger - 05/06/2017 - #15096
*/
create table relatorio_inspecao (
    cd_relatorio_inspecao                INT8            not null,
    cd_req_vigilancia       	         INT8            not null,
    ds_objetivo                          VARCHAR         null,
    ds_historico_situacao_anterior       VARCHAR         null,
    ds_situacao_encontrada               VARCHAR         null,
    ds_irregularidades_constatadas       VARCHAR         null,
    ds_conclusao                         VARCHAR         null,
    ds_medidas_adotadas                  VARCHAR         null,
    dt_cadastro                          DATE            not null,
    version                              INT8            not null,
    constraint PK_RELATORIO_INSPECAO primary key (cd_relatorio_inspecao),
    constraint FK_REL_INSPEC_REF_REQ_VIGI foreign key (cd_req_vigilancia)
      references requerimento_vigilancia (cd_req_vigilancia)
);
CREATE TABLE auditschema.relatorio_inspecao AS SELECT t2.*, t1.* FROM relatorio_inspecao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_relatorio_inspecao;alter table auditschema.relatorio_inspecao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON relatorio_inspecao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create table relatorio_inspecao_equipes (
    cd_relatorio_inspecao_equipes        INT8            not null,
    cd_relatorio_inspecao                INT8            not null,
    cd_profissional         	           INT8            not null,
    version                              INT8            not null,
    constraint PK_RELATORIO_INSPECAO_EQUIPES primary key (cd_relatorio_inspecao_equipes),
    constraint FK_REL_INSPEC_EQUIP_REF_PROF foreign key (cd_relatorio_inspecao)
      references relatorio_inspecao (cd_relatorio_inspecao) on delete restrict on update restrict,
    constraint FK_REL_INSPEC_EQUIP_REF_REL_INSPEC foreign key (cd_profissional)
      references profissional (cd_profissional) on delete restrict on update restrict
);
CREATE TABLE auditschema.relatorio_inspecao_equipes AS SELECT t2.*, t1.* FROM relatorio_inspecao_equipes t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_relatorio_inspecao_equipes;alter table auditschema.relatorio_inspecao_equipes add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON relatorio_inspecao_equipes FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

ALTER TABLE relatorio_inspecao ADD COLUMN cd_mot_visita int8 NULL;
ALTER TABLE auditschema.relatorio_inspecao ADD COLUMN cd_mot_visita int8 NULL;
ALTER TABLE relatorio_inspecao add constraint FK_REL_INSPEC_REF_MOT_VISITA foreign key (cd_mot_visita)
      references motivo_visita (cd_mot_visita) on delete restrict on update restrict;

INSERT INTO programa_pagina VALUES (1406, 'br.com.celk.view.vigilancia.requerimentos.CadastroRelatorioInspecaoPage', 'N');
INSERT INTO programa_web_pagina VALUES (1482, 764, 1406);

/*
    Sulivan - 08/06/2017 - #15137
*/
create table atendimento_classif_atend (
    cd_atendimento_classif_atend 		int8 	not null,
    nr_atendimento                      INT8    not null,
    cd_cla_atendimento                  INT8 	not null,
    version                             INT8 	not null
);

alter table atendimento_classif_atend
	add CONSTRAINT pk_atendimento_classif_atend PRIMARY key (cd_atendimento_classif_atend);

alter table atendimento_classif_atend
   add constraint FK_ATEND_CLASSIF_ATEND_REF_ATENDIMENTO foreign key (nr_atendimento)
      references atendimento (nr_atendimento)
      on delete restrict on update restrict;

alter table atendimento_classif_atend
   add constraint FK_ATEND_CLASSIF_ATEND_REF_CLASSIF_ATEND foreign key (cd_cla_atendimento)
      references classificacao_atendimento (cd_cla_atendimento)
      on delete restrict on update restrict;

CREATE TABLE auditschema.atendimento_classif_atend AS SELECT t2.*, t1.* FROM atendimento_classif_atend t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_classif_atend;alter table auditschema.atendimento_classif_atend add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_classif_atend FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create table ciap_atendimento (
    cd_ciap_atendimento 		int8 	not null,
    nr_atendimento              INT8    not null,
    cd_ciap                     INT8 	not null,
    version                     INT8 	not null
);

alter table ciap_atendimento
	add CONSTRAINT pk_ciap_atendimento PRIMARY key (cd_ciap_atendimento);

alter table ciap_atendimento
   add constraint FK_CIAP_ATEND_REF_ATENDIMENTO foreign key (nr_atendimento)
      references atendimento (nr_atendimento)
      on delete restrict on update restrict;

alter table ciap_atendimento
   add constraint FK_CIAP_ATEND_REF_CIAP foreign key (cd_ciap)
      references ciap (cd_ciap)
      on delete restrict on update restrict;

CREATE TABLE auditschema.ciap_atendimento AS SELECT t2.*, t1.* FROM ciap_atendimento t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_ciap_atendimento;alter table auditschema.ciap_atendimento add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ciap_atendimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

/*
    Sulivan - 08/06/2017 - #15148
*/
INSERT INTO programa_pagina VALUES (1407, 'br.com.celk.view.unidadesaude.atendimento.consulta.consultaatendimentos.ConsultaAtendimentosPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (765, 'Consulta de Atendimentos', 1407, 'N');
INSERT INTO programa_web_pagina VALUES (1483, 765, 1407);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1006,'Consulta de Atendimentos','consultaAtendimentos',642,765,143,0,0);

/*
    Laudecir - 31/05/2017 - #15068
*/
alter table item_inspecao_pergunta add column ds_providencia varchar;
alter table auditschema.item_inspecao_pergunta add column ds_providencia varchar;

alter table roteiro_inspecao alter nm_roteiro type varchar(256);
alter table roteiro_inspecao alter ds_enquad_legal type varchar(512);
alter table auditschema.roteiro_inspecao alter nm_roteiro type varchar(256);
alter table auditschema.roteiro_inspecao alter ds_enquad_legal type varchar(512);

alter table registro_insp_rot_it_perg_resp add column ds_providencia varchar;
alter table auditschema.registro_insp_rot_it_perg_resp add column ds_providencia varchar;

alter table registro_insp_rot_it_perg_resp alter observacao type varchar(256);
alter table auditschema.registro_insp_rot_it_perg_resp alter observacao type varchar(256);

alter table registro_inspecao_roteiro alter nm_roteiro type varchar(256);
alter table registro_inspecao_roteiro alter ds_enquad_legal type varchar(512);
alter table auditschema.registro_inspecao_roteiro alter nm_roteiro type varchar(256);
alter table auditschema.registro_inspecao_roteiro alter ds_enquad_legal type varchar(512);

ALTER TABLE registro_inspecao_roteiro ADD COLUMN cd_roteiro_insp INT8 NULL;
ALTER TABLE registro_inspecao_roteiro ADD COLUMN status INT2 NULL;
ALTER TABLE auditschema.registro_inspecao_roteiro ADD COLUMN cd_roteiro_insp INT8 NULL;
ALTER TABLE auditschema.registro_inspecao_roteiro ADD COLUMN status INT2 NULL;

alter table registro_inspecao_roteiro
   add constraint FK_REG_INSPECAO_ROTEIRO_REF_ROTEIRO foreign key (cd_roteiro_insp)
      references roteiro_inspecao (cd_roteiro_insp)
      on delete restrict on update restrict;

INSERT INTO permissao_web (cd_permissao, ds_permissao, version) VALUES (107, 'Roteiro Inspeção', 0);
INSERT INTO programa_pagina_permissao VALUES (338, 107, 1403, 0, 'roteiroInspecao');

ALTER TABLE registro_inspecao ADD COLUMN cd_req_vigilancia INT8 NULL;
ALTER TABLE auditschema.registro_inspecao ADD COLUMN cd_req_vigilancia INT8 NULL;

alter table registro_inspecao
   add constraint FK_REG_INSPECAO_REF_REQ_VIGILANCIA foreign key (cd_req_vigilancia)
      references requerimento_vigilancia (cd_req_vigilancia)
      on delete restrict on update restrict;

ALTER TABLE registro_insp_rot_it_perg_resp ADD COLUMN cd_reg_insp_rot_it_perg_resp_orig INT8 NULL;
ALTER TABLE auditschema.registro_insp_rot_it_perg_resp ADD COLUMN cd_reg_insp_rot_it_perg_resp_orig INT8 NULL;

alter table registro_insp_rot_it_perg_resp
   add constraint FK_REG_INS_ROT_IT_PERG_REF_REG_INS_ROT_IT_PERG foreign key (cd_reg_insp_rot_it_perg_resp_orig)
      references registro_insp_rot_it_perg_resp (cd_reg_insp_rot_it_perg_resp)
      on delete restrict on update restrict;

update registro_insp_rot_it_perg_resp set cd_reg_insp_rot_it_perg_resp_orig = cd_reg_insp_rot_it_perg_resp where cd_reg_insp_rot_it_perg_resp_orig is null;
update registro_insp_rot_it_perg_resp set resposta = 1 where resposta is null;

update registro_inspecao_roteiro
   set status = 1 -- EM INSPEÇÃO
 where status is null
   and exists(select 1 from registro_inspecao t2 where t2.status = 1 and t2.cd_reg_insp = cd_reg_insp);

update registro_inspecao_roteiro
   set status = 3 -- INSPECIONADO
 where status is null
   and exists(select 1 from registro_inspecao t2 where t2.status = 2 and t2.cd_reg_insp = cd_reg_insp);

/*
    Laudecir - 12/06/2017 - #15019
*/
INSERT INTO programa_web_pagina VALUES (1484, 618, 1079);
alter table auto_intimacao alter ds_exigencia type varchar;
alter table auto_intimacao alter ds_enquadramento_legal type varchar;
alter table auditschema.auto_intimacao alter ds_exigencia type varchar;
alter table auditschema.auto_intimacao alter ds_enquadramento_legal type varchar;

alter table auto_intimacao_exigencia alter ds_exigencia type varchar;
alter table auditschema.auto_intimacao_exigencia alter ds_exigencia type varchar;
alter table auto_intimacao_exigencia alter ds_irregularidade drop not null;
alter table auditschema.auto_intimacao_exigencia alter ds_irregularidade drop not null;

ALTER TABLE auto_intimacao ADD COLUMN cd_motivo_retorno INT8 NULL;
ALTER TABLE auditschema.auto_intimacao ADD COLUMN cd_motivo_retorno INT8 NULL;

alter table auto_intimacao
   add constraint FK_auto_intimacao_REF_motivo_retorno foreign key (cd_motivo_retorno)
      references motivo_retorno (cd_motivo_retorno)
      on delete restrict on update restrict;

alter table configuracao_vigilancia add column tp_calc_prazo_exigencia int2;
alter table auditschema.configuracao_vigilancia add column tp_calc_prazo_exigencia int2;

alter table configuracao_vigilancia add column tp_database_calc_prazo_exigencia int2;
alter table auditschema.configuracao_vigilancia add column tp_database_calc_prazo_exigencia int2;
/*
    Roger - 05/06/2017 - #15096
*/
ALTER TABLE ped_transf_licitacao_item ADD COLUMN flag_separado SMALLINT NULL;
ALTER TABLE auditschema.ped_transf_licitacao_item ADD COLUMN flag_separado SMALLINT NULL;

/*
    Roger - 21/06/2017 - #15159
*/
INSERT INTO programa_pagina_permissao VALUES(339, 18, 260, 0, 'empresa');

/*
    Roger - 26/06/2017 - #15159
*/
create table produto_solicitado_movimento_lote (
    cd_produto_solicitado_movimento_lote        INT8            not null,
    cd_prod_solic_mov                           INT8            not null,
    empresa                                     INTEGER         not null,
    cod_deposito                                INTEGER         not null,
    cod_pro                                     CHAR(13)        not null,
    grupo_estoque                               CHAR(20)        not null,
    quantidade                                  NUMERIC(12,2)   not null,
    version                                     INT8            not null,
    constraint PK_PROD_SOLIC_MOV_LOTE primary key (cd_produto_solicitado_movimento_lote),
    constraint FK_PROD_SOLIC_MOV_LOTE_REF_PROD_SOLIC_MOV foreign key (cd_prod_solic_mov)
      references produto_solicitado_movimento (cd_prod_solic_mov),
    constraint FK_PROD_SOLIC_MOV_LOTE_REF_GRUPO_ESTOQUE foreign key (empresa, cod_deposito, cod_pro, grupo_estoque)
      references grupo_estoque (empresa, cod_deposito, cod_pro, grupo_estoque)
      on delete restrict on update restrict
);
CREATE TABLE auditschema.produto_solicitado_movimento_lote AS SELECT t2.*, t1.* FROM produto_solicitado_movimento_lote t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_produto_solicitado_movimento_lote;alter table auditschema.produto_solicitado_movimento_lote add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON produto_solicitado_movimento_lote FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

/*
    Roger - 28/06/2017 - #15218
*/
ALTER TABLE medicamento_paciente ADD COLUMN receita SMALLINT NULL;
ALTER TABLE auditschema.medicamento_paciente ADD COLUMN receita SMALLINT NULL;

/*
    Maicon - 28/06/2017 - #15244
*/
alter table grupos alter column nm_grupo type varchar;
alter table auditschema.grupos alter column nm_grupo type varchar;

/*
    Maicon - 30/06/2017 - #15259
*/
alter table requerimento_vigilancia add column flag_isento_mei int2;
alter table auditschema.requerimento_vigilancia add column flag_isento_mei int2;

alter table requerimento_vigilancia add column descricao_isento_outro varchar;
alter table auditschema.requerimento_vigilancia add column descricao_isento_outro varchar;

/*
    Sulivan - 30/06/2017 - #15136
*/
INSERT INTO programa_pagina VALUES (1408, 'br.com.celk.view.controle.integracaocnes.IntegracaoCnesPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (766, 'Integração CNES', 1408, 'N');
INSERT INTO programa_web_pagina VALUES (1485, 766, 1408);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1007,'Integração CNES','integracaoCnes',396,766,0,0,0);

INSERT INTO programa_pagina VALUES (1409, 'br.com.celk.view.controle.integracaocnes.DetalhesCnesProcessoOcorrenciaPage', 'N');
INSERT INTO programa_web_pagina VALUES (1486, 766, 1409);

create table cnes_processo (
    cd_cnes_processo            INT8                 not null,
    cd_process                  INT8                 null,
    cd_gerenciador_arquivo      INT8                 null,
    status                      INT2                 not null,
    cd_usuario_validacao        NUMERIC(6)           not null,
    dt_validacao                TIMESTAMP            not null,
    cd_usuario_processamento    NUMERIC(6)           null,
    dt_processamento            TIMESTAMP            null,
    cd_usuario_can              NUMERIC(6)           null,
    dt_cancelamento             TIMESTAMP            null,
    path                        VARCHAR(50)          null,
    msg_erro                    text                 null,
    version                     INT8                 not null
);

alter table cnes_processo
   add constraint PK_CNES_PROCESSO primary key (cd_cnes_processo);

alter table cnes_processo
   add constraint FK_CNES_PROC_REF_ASYNC foreign key (cd_process)
      references async_process (cd_process)
      on delete restrict on update restrict;

alter table cnes_processo
   add constraint FK_CNES_PROC_REF_GEREN_ARQ foreign key (cd_gerenciador_arquivo)
      references gerenciador_arquivo (cd_gerenciador_arquivo)
      on delete restrict on update restrict;

alter table cnes_processo
   add constraint FK_CNES_PROC_REF_USU_VAL foreign key (cd_usuario_validacao)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

alter table cnes_processo
   add constraint FK_CNES_PROC_REF_USU_PROC foreign key (cd_usuario_processamento)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

alter table cnes_processo
   add constraint FK_CNES_PROC_REF_USU_CAN foreign key (cd_usuario_can)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

CREATE TABLE auditschema.cnes_processo AS SELECT t2.*, t1.* FROM cnes_processo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cnes_processo;alter table auditschema.cnes_processo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cnes_processo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create table cnes_processo_empresa (
    cd_cnes_processo_empresa        INT8                 not null,
    cd_cnes_processo                INT8                 not null,
    unidade_id                      VARCHAR(50)          not null,
    cnes                            VARCHAR(7)           null,
    cnpj_mant                       VARCHAR(15)          null,
    pfpj_ind                        VARCHAR(1)           null,
    nivel_dep                       VARCHAR(1)           null,
    razao_social                    VARCHAR(100)         not null,
    nome_fantasia                   VARCHAR(100)         not null,
    logradouro                      VARCHAR(100)         not null,
    numero                          VARCHAR(15)          null,
    complemento                     VARCHAR(30)          null,
    bairro                          VARCHAR(100)         not null,
    cep                             VARCHAR(15)          not null,
    reg_saude		                VARCHAR(10)          null,
    micro_reg		                VARCHAR(10)          null,
    dist_sanit		                VARCHAR(10)          null,
    dist_admin		                VARCHAR(10)          null,
    telefone		                VARCHAR(15)          null,
    fax		                        VARCHAR(15)          null,
    email		                    VARCHAR(100)         null,
    cpf		                        VARCHAR(11)          null,
    cnpj		                    VARCHAR(14)          null,
    cod_natureza_jur		        VARCHAR(10)          null,
    cod_ativ		                VARCHAR(3)           null,
    cod_cliente                     VARCHAR(2)           null,
    num_alvara                      VARCHAR(60)          null,
    dt_expedicao_alvara             date                 null,
    ind_orgexp                      VARCHAR(2)           null,
    tp_unid_id                      VARCHAR(2)           null,
    cod_turnat                      VARCHAR(2)           null,
    sigestgest                      VARCHAR(2)           null,
    cod_mungest                     VARCHAR(10)          null,
    status_mov                      VARCHAR(1)           not null,
    dt_atualizacao                  date                 not null,
    usuario_atualizacao             VARCHAR(20)          not null,
    version                         INT8                 not null
);

alter table cnes_processo_empresa
    add constraint PK_CNES_PROCESSO_EMP primary key (cd_cnes_processo_empresa);

alter table cnes_processo_empresa
    add constraint FK_CNES_PROC_EMP_REF_CNES_PROC foreign key (cd_cnes_processo)
        references cnes_processo (cd_cnes_processo)
        on delete restrict on update restrict;

CREATE TABLE auditschema.cnes_processo_empresa AS SELECT t2.*, t1.* FROM cnes_processo_empresa t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cnes_processo_empresa;alter table auditschema.cnes_processo_empresa add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cnes_processo_empresa FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create table cnes_processo_profissional (
    cd_cnes_processo_profissional   INT8                 not null,
    cd_cnes_processo_empresa        INT8                 not null,
    prof_id                         VARCHAR(50)          not null,
    cpf_prof                        VARCHAR(11)          not null,
    pis_pasep                       VARCHAR(15)          null,
    nome_prof                       VARCHAR(100)         not null,
    nome_mae                        VARCHAR(100)         null,
    dt_nascimento                   date                 null,
    cod_municipio                   VARCHAR(10)          null,
    sexo                            VARCHAR(1)           null,
    num_livro                       VARCHAR(10)          null,
    num_folha                       VARCHAR(10)          null,
    num_termo                       VARCHAR(10)          null,
    cod_org_emis                    VARCHAR(2)           null,
    dt_emissao                      date                 null,
    num_identidade                  VARCHAR(15)          null,
    sigla_estado                    VARCHAR(2)           null,
    dt_emissao_ident                date                 null,
    dt_entrada                      date                 null,
    ctps_numer                      VARCHAR(10)          null,
    serie                           VARCHAR(5)           null,
    sigla_est_ctps                  VARCHAR(2)           null,
    dt_emissao_ctps                 date                 null,
    logradouro                      VARCHAR(100)         null,
    numero                          VARCHAR(10)          null,
    complemento                     VARCHAR(100)         null,
    bairro                          VARCHAR(100)         null,
    cep                             VARCHAR(15)          null,
    sigla_uf                        VARCHAR(2)           null,
    cod_escolar                     VARCHAR(2)           null,
    cod_certidao                    VARCHAR(2)           null,
    cod_nacionalidade               VARCHAR(1)           null,
    nome_cartorio                   VARCHAR(100)         null,
    cod_banco                       VARCHAR(3)           null,
    nome_pais                       VARCHAR(100)         null,
    num_agencia                     VARCHAR(5)           null,
    conta_cc                        VARCHAR(15)          null,
    cod_cns                         VARCHAR(15)          null,
    d_tercsih                       VARCHAR(1)           null,
    status                          VARCHAR(1)           null,
    status_mov                      VARCHAR(1)           not null,
    dt_atualizacao                  date                 not null,
    usuario                         VARCHAR(20)          not null,
    cod_raca                        VARCHAR(2)           null,
    telefone		                VARCHAR(15)          null,
    nome_pai		                VARCHAR(100)         null,
    cd_tp_logradouro		        VARCHAR(3)           null,
    portaria		                VARCHAR(20)          null,
    dt_naturalizacao                date                 null,
    cod_pais		                VARCHAR(3)           null,
    version                         INT8                 not null
);

alter table cnes_processo_profissional
    add constraint PK_CNES_PROCESSO_PROF primary key (cd_cnes_processo_profissional);

alter table cnes_processo_profissional
    add constraint FK_CNES_PROC_PROF_REF_CNES_PROC_EMP foreign key (cd_cnes_processo_empresa)
        references cnes_processo_empresa (cd_cnes_processo_empresa)
        on delete restrict on update restrict;

CREATE TABLE auditschema.cnes_processo_profissional AS SELECT t2.*, t1.* FROM cnes_processo_profissional t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cnes_processo_profissional;alter table auditschema.cnes_processo_profissional add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cnes_processo_profissional FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create table cnes_processo_prof_vinculo (
    cd_cnes_processo_prof_vinculo      INT8                 not null,
    cd_cnes_processo_profissional      INT8                 not null,
    cod_cbo                            VARCHAR(10)          not null,
    ind_vinc                           VARCHAR(10)          not null,
    qtd_carga_hora_outro               INT8                 null,
    qtd_carga_hora_ambu                INT8                 null,
    qtd_carga_hora_hosp                INT8                 null,
    cod_conselho                       VARCHAR(5)           null,
    num_registro                       VARCHAR(60)          null,
    vinculo_sus                        VARCHAR(5)           null,
    usuario		                       VARCHAR(100)         not null,
    unidade_id                         VARCHAR(50)          not null,
    version                            INT8                 not null
);

alter table cnes_processo_prof_vinculo
    add constraint PK_CNES_PROCESSO_PROF_VINC primary key (cd_cnes_processo_prof_vinculo);

alter table cnes_processo_prof_vinculo
    add constraint FK_CNES_PROC_PROF_VIN_REF_CNES_PROC_PROF foreign key (cd_cnes_processo_profissional)
        references cnes_processo_profissional (cd_cnes_processo_profissional)
        on delete restrict on update restrict;

CREATE TABLE auditschema.cnes_processo_prof_vinculo AS SELECT t2.*, t1.* FROM cnes_processo_prof_vinculo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cnes_processo_prof_vinculo;alter table auditschema.cnes_processo_prof_vinculo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cnes_processo_prof_vinculo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create table cnes_processo_equipe (
    cd_cnes_processo_equipe         INT8                 not null,
    cd_cnes_processo_empresa        INT8                 not null,
    cod_municipio                   VARCHAR(10)          not null,
    cod_area                        VARCHAR(10)          not null,
    seq_equipe                      INT8                 not null,
    ds_area                         VARCHAR(100)         not null,
    unidade_id                      VARCHAR(50)          not null,
    tp_equipe                       VARCHAR(5)           not null,
    ds_equipe                       VARCHAR(100)         null,
    nm_referencia                   VARCHAR(100)         null,
    cod_segmento                    VARCHAR(2)           null,
    ds_segmento                     VARCHAR(100)         null,
    tp_segmento                     VARCHAR(1)           null,
    tp_pop_assist_quilomb           VARCHAR(1)           null,
    tp_pop_assist_assent            VARCHAR(1)           null,
    tp_pop_assist_geral             VARCHAR(1)           null,
    tp_pop_assist_escola            VARCHAR(1)           null,
    tp_pop_assist_pronasci          VARCHAR(1)           null,
    dt_ativacao                     date                 null,
    dt_desativacao                  date                 null,
    cod_tp_desativacao              VARCHAR(15)          null,
    cod_moti_desativacao            VARCHAR(15)          null,
    usuario                         VARCHAR(100)         null,
    cod_equipe                      VARCHAR(15)          null,
    version                         INT8                 not null
);

alter table cnes_processo_equipe
    add constraint PK_CNES_PROCESSO_EQUIPE primary key (cd_cnes_processo_equipe);

alter table cnes_processo_equipe
    add constraint FK_CNES_PROC_EQUI_REF_CNES_PROC_EMP foreign key (cd_cnes_processo_empresa)
        references cnes_processo_empresa (cd_cnes_processo_empresa)
        on delete restrict on update restrict;

CREATE TABLE auditschema.cnes_processo_equipe AS SELECT t2.*, t1.* FROM cnes_processo_equipe t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cnes_processo_equipe;alter table auditschema.cnes_processo_equipe add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cnes_processo_equipe FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create table cnes_processo_equipe_prof (
    cd_cnes_processo_equipe_prof        INT8                 not null,
    cd_cnes_processo_equipe             INT8                 not null,
    prof_id                             VARCHAR(50)          not null,
    cod_cbo                             VARCHAR(10)          not null,
    ind_vinc                            VARCHAR(10)          not null,
    tp_sus_nao_sus                      VARCHAR(1)           not null,
    cod_hora_amb                        INT8                 null,
    cod_hora_hosp                       INT8                 null,
    cod_hora_outro                      INT8                 null,
    fl_equipe_minima                    VARCHAR(1)           null,
    micro_area                          VARCHAR(20)          null,
    cnes_outra_equipe                   VARCHAR(7)           null,
    cod_munic_outra_equipe              VARCHAR(10)          null,
    dt_entrada                          date                 null,
    dt_desligamento                     date                 null,
    cnes_atend_comp_1                   VARCHAR(7)           null,
    cnes_atend_comp_2                   VARCHAR(7)           null,
    cnes_atend_comp_3                   VARCHAR(7)           null,
    cnes_1_ch_difer_sistpenit           VARCHAR(7)           null,
    cnes_1_ch_difer_hpp                 VARCHAR(7)           null,
    ch_outros_ch_difer_resmed           VARCHAR(7)           null,
    usuario                             VARCHAR(100)         null,
    version                             INT8                 not null
);

alter table cnes_processo_equipe_prof
    add constraint PK_CNES_PROCESSO_EQUI_PROF primary key (cd_cnes_processo_equipe_prof);

alter table cnes_processo_equipe_prof
    add constraint FK_CNES_PROC_EQ_PROF_REF_CNES_PROC_EQ foreign key (cd_cnes_processo_equipe)
        references cnes_processo_equipe (cd_cnes_processo_equipe)
        on delete restrict on update restrict;

CREATE TABLE auditschema.cnes_processo_equipe_prof AS SELECT t2.*, t1.* FROM cnes_processo_equipe_prof t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cnes_processo_equipe_prof;alter table auditschema.cnes_processo_equipe_prof add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cnes_processo_equipe_prof FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create table cnes_processo_habilitacao (
    cd_cnes_processo_habilitacao            INT8                    not null,
    cd_cnes_processo_empresa                INT8                    not null,
    cod_habilitacao                         VARCHAR(10)             not null,
    ds_habilitacao                          VARCHAR(100)            not null,
    cmpt_inicial                            VARCHAR(10)             not null,
    cmpt_final                              VARCHAR(10)             not null,
    qt_leitos                               INT8                    not null,
    num_portaria                            VARCHAR(50)             null,
    dt_lancamento                           date                    null,
    usuario                                 VARCHAR(100)            null,
    version                                 INT8                    not null
);

alter table cnes_processo_habilitacao
    add constraint PK_CNES_PROCESSO_HABI primary key (cd_cnes_processo_habilitacao);

alter table cnes_processo_habilitacao
    add constraint FK_CNES_PROC_HABI_REF_CNES_PROC_EMP foreign key (cd_cnes_processo_empresa)
        references cnes_processo_empresa (cd_cnes_processo_empresa)
        on delete restrict on update restrict;

CREATE TABLE auditschema.cnes_processo_habilitacao AS SELECT t2.*, t1.* FROM cnes_processo_habilitacao t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cnes_processo_habilitacao;alter table auditschema.cnes_processo_habilitacao add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cnes_processo_habilitacao FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create table cnes_processo_serv_especializados (
    cd_cnes_processo_serv_especializados            INT8                    not null,
    cd_cnes_processo_empresa                        INT8                    not null,
    cod_servico                                     VARCHAR(5)              not null,
    ds_servico                                      VARCHAR(100)            not null,
    cod_caracteristica                              VARCHAR(1)              not null,
    cod_serv_ambul                                  VARCHAR(1)              null,
    cod_serv_ambul_sus                              VARCHAR(1)              null,
    cod_serv_hosp                                   VARCHAR(1)              null,
    cod_serv_hosp_sus                               VARCHAR(1)              null,
    cod_class                                       VARCHAR(5)              not null,
    cnpj_cpf                                        VARCHAR(15)             not null,
    cod_end_compl                                   VARCHAR(10)             null,
    ds_end_compl                                    VARCHAR(100)            null,
    ds_class                                        VARCHAR(100)            null,
    usuario                                         VARCHAR(100)            null,
    version                                         INT8                    not null
);

alter table cnes_processo_serv_especializados
    add constraint PK_CNES_PROCESSO_SERV_ESPEC primary key (cd_cnes_processo_serv_especializados);

alter table cnes_processo_serv_especializados
    add constraint FK_CNES_PROC_SERV_ESPEC_REF_CNES_PROC_EMP foreign key (cd_cnes_processo_empresa)
        references cnes_processo_empresa (cd_cnes_processo_empresa)
        on delete restrict on update restrict;

CREATE TABLE auditschema.cnes_processo_serv_especializados AS SELECT t2.*, t1.* FROM cnes_processo_serv_especializados t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cnes_processo_serv_especializados;alter table auditschema.cnes_processo_serv_especializados add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cnes_processo_serv_especializados FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create table cnes_processo_ocorrencia (
    cd_cnes_processo_ocorrencia             INT8                    not null,
    cd_cnes_processo                        INT8                    not null,
    cd_cnes_processo_empresa                INT8                    null,
    cd_cnes_processo_profissional           INT8                    null,
    cd_cnes_processo_serv_especializados    INT8                    null,
    cd_cnes_processo_equipe                 INT8                    null,
    cd_cnes_processo_equipe_prof            INT8                    null,
    empresa          				        INT8                    null,
    cd_profissional          			    INT8                    null,
    cd_prof_carga_horaria                   INT8                    null,
    cd_emp_serv_cla                         INT8                    null,
    cd_equipe                               INT8                    null,
    cd_equipe_profissional                  INT8                    null,
    dt_ocorrencia        			        timestamp               not null,
    ds_ocorrencia                           TEXT                    not null,
    cd_usuario                              NUMERIC(6)              not null,
    tipo                                    INT2                    not null,
    origem                                  INT2                    not null,
    version                                 INT8                    not null
);

alter table cnes_processo_ocorrencia
   add constraint PK_CNES_PROC_OCO primary key (cd_cnes_processo_ocorrencia);

alter table cnes_processo_ocorrencia
   add constraint FK_CNES_PROC_OCO_REF_CNES_PROC foreign key (cd_cnes_processo)
      references cnes_processo (cd_cnes_processo)
      on delete restrict on update restrict;

alter table cnes_processo_ocorrencia
   add constraint FK_CNES_PROC_OCO_REF_CNES_PROC_EMP foreign key (cd_cnes_processo_empresa)
      references cnes_processo_empresa (cd_cnes_processo_empresa)
      on delete restrict on update restrict;

alter table cnes_processo_ocorrencia
   add constraint FK_CNES_PROC_OCO_REF_CNES_PROC_PROF foreign key (cd_cnes_processo_profissional)
      references cnes_processo_profissional (cd_cnes_processo_profissional)
      on delete restrict on update restrict;

alter table cnes_processo_ocorrencia
   add constraint FK_CNES_PROC_OCO_REF_CNES_PROC_SERV_ESP foreign key (cd_cnes_processo_serv_especializados)
      references cnes_processo_serv_especializados (cd_cnes_processo_serv_especializados)
      on delete restrict on update restrict;

alter table cnes_processo_ocorrencia
   add constraint FK_CNES_PROC_OCO_REF_CNES_PROC_EQ foreign key (cd_cnes_processo_equipe)
      references cnes_processo_equipe (cd_cnes_processo_equipe)
      on delete restrict on update restrict;

alter table cnes_processo_ocorrencia
   add constraint FK_CNES_PROC_OCO_REF_CNES_PROC_EQ_PROF foreign key (cd_cnes_processo_equipe_prof)
      references cnes_processo_equipe_prof (cd_cnes_processo_equipe_prof)
      on delete restrict on update restrict;

alter table cnes_processo_ocorrencia
   add constraint FK_CNES_PROC_OCO_REF_EMPR foreign key (empresa)
      references empresa (empresa)
      on delete restrict on update restrict;

alter table cnes_processo_ocorrencia
   add constraint FK_CNES_PROC_OCO_REF_PROF foreign key (cd_profissional)
      references profissional (cd_profissional)
      on delete restrict on update restrict;

alter table cnes_processo_ocorrencia
   add constraint FK_CNES_PROC_OCO_REF_PROF_CAR_HOR foreign key (cd_prof_carga_horaria)
      references profissional_carga_horaria (cd_prof_carga_horaria)
      on delete restrict on update restrict;

alter table cnes_processo_ocorrencia
   add constraint FK_CNES_PROC_OCO_REF_EMP_SERV_CLA foreign key (cd_emp_serv_cla)
      references empresa_servico_class (cd_emp_serv_cla)
      on delete restrict on update restrict;

alter table cnes_processo_ocorrencia
   add constraint FK_CNES_PROC_OCO_REF_EQUIPE foreign key (cd_equipe)
      references equipe (cd_equipe)
      on delete restrict on update restrict;

alter table cnes_processo_ocorrencia
   add constraint FK_CNES_PROC_OCO_REF_EQ_PROF foreign key (cd_equipe_profissional)
      references equipe_profissional (cd_equipe_profissional)
      on delete restrict on update restrict;

alter table cnes_processo_ocorrencia
   add constraint FK_CNES_PROC_OCO_REF_USU foreign key (cd_usuario)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

CREATE TABLE auditschema.cnes_processo_ocorrencia AS SELECT t2.*, t1.* FROM cnes_processo_ocorrencia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_cnes_processo_ocorrencia;alter table auditschema.cnes_processo_ocorrencia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON cnes_processo_ocorrencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

alter table profissional add column cd_cnes_processo INT8 null;
alter table auditschema.profissional add column cd_cnes_processo INT8 null;

alter table profissional
   add constraint FK_PROF_REF_CNES_PROC foreign key (cd_cnes_processo)
      references cnes_processo (cd_cnes_processo)
      on delete restrict on update restrict;

alter table empresa add column cd_cnes_processo INT8 null;
alter table auditschema.empresa add column cd_cnes_processo INT8 null;

alter table empresa
   add constraint FK_EMPR_REF_CNES_PROC foreign key (cd_cnes_processo)
      references cnes_processo (cd_cnes_processo)
      on delete restrict on update restrict;

alter table empresa_servico_class add column cd_cnes_processo INT8 null;
alter table auditschema.empresa_servico_class add column cd_cnes_processo INT8 null;

alter table empresa_servico_class
   add constraint FK_EMP_SERV_CLASS_REF_CNES_PROC foreign key (cd_cnes_processo)
      references cnes_processo (cd_cnes_processo)
      on delete restrict on update restrict;

alter table profissional_carga_horaria add column cd_cnes_processo INT8 null;
alter table auditschema.profissional_carga_horaria add column cd_cnes_processo INT8 null;

alter table profissional_carga_horaria
   add constraint FK_PROF_CAR_HOR_REF_CNES_PROC foreign key (cd_cnes_processo)
      references cnes_processo (cd_cnes_processo)
      on delete restrict on update restrict;

alter table equipe add column cd_cnes_processo INT8 null;
alter table auditschema.equipe add column cd_cnes_processo INT8 null;

alter table equipe
   add constraint FK_EQUI_REF_CNES_PROC foreign key (cd_cnes_processo)
      references cnes_processo (cd_cnes_processo)
      on delete restrict on update restrict;

alter table equipe_profissional add column cd_cnes_processo INT8 null;
alter table auditschema.equipe_profissional add column cd_cnes_processo INT8 null;

alter table equipe_profissional
   add constraint FK_EQ_PROF_REF_CNES_PROC foreign key (cd_cnes_processo)
      references cnes_processo (cd_cnes_processo)
      on delete restrict on update restrict;

/*
    Roger - 03/07/2017 - #15266
*/
create table atividades_vigilancia (
    cd_atividades_vigilancia             BIGINT            not null,
    descricao                            VARCHAR(200)           not null,
    cd_procedimento                      numeric(10)       not null,
    dt_cadastro                          TIMESTAMP         not null,
    dt_alteracao                         TIMESTAMP         not null,
    cd_usuario                           NUMERIC(6)        not null,
    version                              BIGINT            not null,
    constraint PK_ATIV_VIG primary key (cd_atividades_vigilancia),
    constraint FK_ATIV_VIG foreign key (cd_procedimento)
      references procedimento (cd_procedimento),
    constraint FK_LANC_ATIV_VIG_SANI_REF_USU foreign key (cd_usuario)
      references usuarios (cd_usuario)
);
CREATE TABLE auditschema.atividades_vigilancia AS SELECT t2.*, t1.* FROM atividades_vigilancia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atividades_vigilancia;alter table auditschema.atividades_vigilancia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atividades_vigilancia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create table lancamento_atividades_vigilancia (
    cd_lancamento_atividades_vigilancia             BIGINT            not null,
    cd_profissional                                 BIGINT            not null,
    dt_atividade                                    DATE              not null,
    observacao                                      VARCHAR           null,
    dt_cadastro                                     TIMESTAMP         not null,
    dt_alteracao                                    TIMESTAMP         not null,
    cd_usuario                                      NUMERIC(6)        not null,
    version                                         BIGINT            not null,
    constraint PK_LANC_ATIV_VIG primary key (cd_lancamento_atividades_vigilancia),
    constraint FK_LANC_ATIV_VIG_REF_PROF foreign key (cd_profissional)
      references profissional (cd_profissional),
    constraint FK_LANC_ATIV_VIG_REF_USU foreign key (cd_usuario)
      references usuarios (cd_usuario)
);
CREATE TABLE auditschema.lancamento_atividades_vigilancia AS SELECT t2.*, t1.* FROM lancamento_atividades_vigilancia t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_lancamento_atividades_vigilancia;alter table auditschema.lancamento_atividades_vigilancia add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON lancamento_atividades_vigilancia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create table lancamento_atividades_vigilancia_item (
    cd_lancamento_atividades_vigilancia_item             BIGINT            not null,
    cd_lancamento_atividades_vigilancia                  BIGINT            not null,
    cd_atividades_vigilancia                             BIGINT            not null,
    quantidade                                           BIGINT            not null,
    dt_cadastro                                          TIMESTAMP         not null,
    dt_alteracao                                         TIMESTAMP         not null,
    cd_usuario                                           NUMERIC(6)        not null,
    version                                              BIGINT            not null,
    constraint PK_LANC_ATIV_VIG_ITEM primary key (cd_lancamento_atividades_vigilancia_item),
    constraint FK_LANC_ATIV_VIG_ITEM_REF_PROF_LANC_ATIV_VIG_SANI foreign key (cd_lancamento_atividades_vigilancia)
      references lancamento_atividades_vigilancia (cd_lancamento_atividades_vigilancia),
    constraint FK_LANC_ATIV_VIG_REF_ATIV_VIG_SANI foreign key (cd_atividades_vigilancia)
      references atividades_vigilancia (cd_atividades_vigilancia),
    constraint FK_LANC_ATIV_VIG_REF_USU foreign key (cd_usuario) references usuarios (cd_usuario)
);
CREATE TABLE auditschema.lancamento_atividades_vigilancia_item AS SELECT t2.*, t1.* FROM lancamento_atividades_vigilancia_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_lancamento_atividades_vigilancia_item;alter table auditschema.lancamento_atividades_vigilancia_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON lancamento_atividades_vigilancia_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

INSERT INTO programa_pagina VALUES (1413, 'br.com.celk.view.vigilancia.faturamento.CadastroAtividadesVigilanciaPage', 'N');
INSERT INTO programa_pagina VALUES (1414, 'br.com.celk.view.vigilancia.faturamento.ConsultaAtividadesVigilanciaPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (770, 'Atividades da Vigilância', 1414, 'N');
INSERT INTO programa_web_pagina VALUES (1490, 770, 1414);
INSERT INTO programa_web_pagina VALUES (1491, 770, 1413);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1011,'Atividades da Vigilância','atividadesVigilancia',308,770,307,0,0);

INSERT INTO programa_pagina VALUES (1415, 'br.com.celk.view.vigilancia.faturamento.lancamentoAtividades.ConsultaLancamentoAtividadesVigilanciaPage', 'N');
INSERT INTO programa_pagina VALUES (1416, 'br.com.celk.view.vigilancia.faturamento.lancamentoAtividades.CadastroLancamentoAtividadesVigilanciaPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (771, 'Lançamento de Atividades da Vigilância', 1415, 'N');
INSERT INTO programa_web_pagina VALUES (1492, 771, 1415);
INSERT INTO programa_web_pagina VALUES (1493, 771, 1416);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1012,'Lançamento de Atividades da Vigilância','lancamentoAtividadesVigilancia',654,771,307,0,0);


ALTER TABLE conta_paciente ADD COLUMN cd_lancamento_atividades_vigilancia BIGINT NULL;
ALTER TABLE auditschema.conta_paciente ADD COLUMN cd_lancamento_atividades_vigilancia BIGINT NULL;

alter table conta_paciente
   add constraint FK_CONT_PAC_REF_LANC_ATIV_VIG foreign key (cd_lancamento_atividades_vigilancia)
      references lancamento_atividades_vigilancia (cd_lancamento_atividades_vigilancia)
      on delete restrict on update restrict;


ALTER TABLE item_conta_paciente ADD COLUMN cd_lancamento_atividades_vigilancia_item BIGINT NULL;
ALTER TABLE auditschema.item_conta_paciente ADD COLUMN cd_lancamento_atividades_vigilancia_item BIGINT NULL;

alter table item_conta_paciente
   add constraint FK_ITEM_CONT_PAC_REF_LANC_ATIV_VIG_ITEM foreign key (cd_lancamento_atividades_vigilancia_item)
      references lancamento_atividades_vigilancia_item (cd_lancamento_atividades_vigilancia_item)
      on delete restrict on update restrict;

/*
    Maicon - 05/07/2017 - #15281
*/
alter table visita_domiciliar add peso numeric(12,2);
alter table auditschema.visita_domiciliar add peso numeric(12,2);

alter table visita_domiciliar add altura numeric(12,2);
alter table auditschema.visita_domiciliar add altura numeric(12,2);

/*
 PostgreSQL
 Everton - 05/07/2017 #15251
*/

create table t_proc_prenatal_ate (cd_prenatal int8, nr_atendimento int8, dt_atendimento timestamp, cd_atend_prenatal int8, qtdade int8);

insert into t_proc_prenatal_ate select cd_prenatal, t1.nr_atendimento, t2.dt_atendimento, min(cd_atend_prenatal), count(*) from atendimento_prenatal t1, atendimento t2
  where t1.nr_atendimento = t2.nr_atendimento
  group by 1, 2, 3 having count(*) > 1;

delete from atendimento_prenatal t1
 where exists (select 1 from t_proc_prenatal_ate where nr_atendimento = t1.nr_atendimento)
   and not exists (select 1 from t_proc_prenatal_ate where cd_atend_prenatal = t1.cd_atend_prenatal);

drop table t_proc_prenatal_ate;

/*
    Maicon - 06/07/2017 - #15293
*/
alter table profissional alter column referencia drop not null;

/*
    Laudecir - 07/07/2017 - #15294
*/
create table movimentacao_vacinas (
	cd_movimentacao_vac         int8            not null,
	empresa                     int8            not null,
	dt_competencia              date            not null,
    status                      int2            not null,
    dt_cadastro                 date            not null,
    dt_usuario                  date            not null,
    cd_usuario                  int8            not null,
	version                     int8            not null,
	constraint PK_MOVIMENTACAO_VACINAS primary key (cd_movimentacao_vac),
	constraint FK_MOVIMENTACAO_VACINAS_REF_EMPRESA foreign key (empresa) references empresa (empresa) on delete restrict on update restrict,
	constraint FK_MOVIMENTACAO_VACINAS_REF_USUARIO foreign key (cd_usuario) references usuarios (cd_usuario) on delete restrict on update restrict
);

CREATE TABLE auditschema.movimentacao_vacinas AS SELECT t2.*, t1.* FROM movimentacao_vacinas t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_movimentacao_vacinas;alter table auditschema.movimentacao_vacinas add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON movimentacao_vacinas FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create table movimentacao_vacinas_item (
	cd_mov_vac_item             int8            not null,
	cd_movimentacao_vac         int8            not null,
	cod_pro                     varchar(13)     not null,
	saldo_anterior              numeric(12,2)   not null,
	qt_recebidas                numeric(12,2)   not null,
	qt_transferidas             numeric(12,2)   not null,
	qt_utilizadas               numeric(12,2)   not null,
	qt_quebradas                numeric(12,2)   not null,
	qt_distribuidas             numeric(12,2)   not null,
	qt_perda_falta_energia      numeric(12,2)   not null,
	qt_perda_proced_inadequado  numeric(12,2)   not null,
	qt_perda_falha_equipamento  numeric(12,2)   not null,
	qt_perda_valid_venc         numeric(12,2)   not null,
	qt_perda_transporte         numeric(12,2)   not null,
	qt_perda_outros_motivos     numeric(12,2)   not null,
	saldo_atual                 numeric(12,2)   not null,
	status                      int2            not null,
    dt_cadastro                 date            not null,
    dt_usuario                  date            not null,
    cd_usuario                  int8            not null,
	version                     int8            not null,
	constraint PK_MOVIMENTACAO_VACINAS_ITEM primary key (cd_mov_vac_item),
	constraint FK_MOVIMENTACAO_VACINAS_ITEM_REF_MOV_VACINAS foreign key (cd_movimentacao_vac) references movimentacao_vacinas (cd_movimentacao_vac) on delete restrict on update restrict,
	constraint FK_MOVIMENTACAO_VACINAS_ITEM_REF_PRODUTO foreign key (cod_pro) references produtos (cod_pro) on delete restrict on update restrict,
	constraint FK_MOVIMENTACAO_VACINAS_ITEM_REF_USUARIO foreign key (cd_usuario) references usuarios (cd_usuario) on delete restrict on update restrict
);

CREATE TABLE auditschema.movimentacao_vacinas_item AS SELECT t2.*, t1.* FROM movimentacao_vacinas_item t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_movimentacao_vacinas_item;alter table auditschema.movimentacao_vacinas_item add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON movimentacao_vacinas_item FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

INSERT INTO programa_pagina VALUES (1417, 'br.com.celk.view.vacina.processo.pni.ConsultaMovimentacaoVacinasPage', 'N');
INSERT INTO programa_pagina VALUES (1418, 'br.com.celk.view.vacina.processo.pni.CadastroMovimentacaoVacinasStep1Page', 'N');
INSERT INTO programa_pagina VALUES (1419, 'br.com.celk.view.vacina.processo.pni.CadastroMovimentacaoVacinasStep2Page', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (772, 'Movimentação das Vacinas', 1417, 'N');
INSERT INTO programa_web_pagina VALUES (1494, 772, 1417);
INSERT INTO programa_web_pagina VALUES (1495, 772, 1418);
INSERT INTO programa_web_pagina VALUES (1496, 772, 1419);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1013, 'Movimentação das Vacinas','movimentacaoVacinas', 714, 772, 18, 0, 0);

/*
    Roger - 06/07/2017 - #15269
*/
INSERT INTO programa_pagina VALUES (1420, 'br.com.celk.view.vigilancia.faturamento.relatorios.RelatorioProducaoVigilanciaDetalhadoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (773, 'Relatório de Produção da Vigilância Sanitária', 1420, 'N');
INSERT INTO programa_web_pagina VALUES (1497, 773, 1420);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1014,'Relatório de Produção','relatorioProducao',837,773,307,0,0);

/*
    Roger - 10/07/2017 - #15276
*/
INSERT INTO programa_pagina VALUES (1422, 'br.com.celk.view.vigilancia.faturamento.relatorios.RelatorioProducaoVigilanciaResumidoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (775, 'Relatório de Produção da Vigilância Sanitária Resumido', 1422, 'N');
INSERT INTO programa_web_pagina VALUES (1499, 775, 1422);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1016,'Relatório de Produção Resumido','relatorioProducaoResumido',837,775,307,0,0);

/*
    Maicon - 11/07/2017 - #15278
*/

alter table auto_intimacao add column cnpj_cpf_pessoa varchar(15) null;
alter table auditschema.auto_intimacao add column cnpj_cpf_pessoa varchar(15) null;

alter table auto_intimacao add column nm_fantasia_pessoa varchar null;
alter table auditschema.auto_intimacao add column nm_fantasia_pessoa varchar null;

alter table auto_intimacao add column representante_legal_pessoa varchar null;
alter table auditschema.auto_intimacao add column representante_legal_pessoa varchar null;

alter table auto_intimacao add column atividade_pessoa varchar null;
alter table auditschema.auto_intimacao add column atividade_pessoa varchar null;
update programa_pagina set cam_pagina = 'br.com.celk.view.vacina.processo.pni.ConsultaConfirmacaoMovimentacaoVacinasPage' where cd_prg_pagina = 1417;
update programa_pagina set cam_pagina = 'br.com.celk.view.vacina.processo.pni.CadastroConfirmacaoMovimentacaoVacinasStep1Page' where cd_prg_pagina = 1418;
update programa_pagina set cam_pagina = 'br.com.celk.view.vacina.processo.pni.CadastroConfirmacaoMovimentacaoVacinasStep2Page' where cd_prg_pagina = 1419;
update programa_web set ds_prg_web = 'Confirmação da Movimentação das Vacinas' where cd_prg_web = 772;
update menu_web set ds_menu = 'Confirmação da Movimentação das Vacinas', ds_bundle = 'confirmacaoMovimentacaoVacinas' where cd_menu = 1013;

/*
    Maicon - 13/07/2017 - #15337
*/
alter table exame_apac alter column cd_cid_principal drop not null;
alter table exame_bpai alter column cd_cid_principal drop not null;

/*
    Laudecir - 15/07/2017 - #15294
*/
alter table movimentacao_vacinas_item add column saldo_indisp_anterior numeric(12,2) not null;
alter table movimentacao_vacinas_item add column saldo_indisp_atual numeric(12,2) not null;
alter table auditschema.movimentacao_vacinas_item add column saldo_indisp_anterior numeric(12,2) not null;
alter table auditschema.movimentacao_vacinas_item add column saldo_indisp_atual numeric(12,2) not null;
alter table movimentacao_vacinas_item drop constraint FK_MOVIMENTACAO_VACINAS_ITEM_REF_PRODUTO;
alter table movimentacao_vacinas_item RENAME cod_pro to cd_produto_vacina;
alter table movimentacao_vacinas_item ALTER COLUMN cd_produto_vacina TYPE int8 USING cd_produto_vacina::bigint;
alter table auditschema.movimentacao_vacinas_item RENAME cod_pro to cd_produto_vacina;
alter table auditschema.movimentacao_vacinas_item ALTER COLUMN cd_produto_vacina TYPE int8 USING cd_produto_vacina::bigint;
alter table movimentacao_vacinas_item
	add constraint FK_MOVIMENTACAO_VACINAS_ITEM_REF_PRODUTO_VACINA foreign key (cd_produto_vacina)
        references produto_vacina (cd_produto_vacina)
            on delete restrict on update restrict;

/*
    Laudecir - 17/07/2017 - #15294
*/
INSERT INTO programa_pagina_permissao VALUES(340, 18, 1417, 0, 'unidade');

/*
    Laudecir - 18/07/2017 - #15381
*/
alter table atividade_grupo alter cd_empresa_bpa drop not null;
alter table atividade_grupo_procedimento add column cd_profissional int8;
alter table atividade_grupo_procedimento add column empresa_bpa int8;
alter table auditschema.atividade_grupo_procedimento add column cd_profissional int8;
alter table auditschema.atividade_grupo_procedimento add column empresa_bpa int8;

alter table atividade_grupo_procedimento
	add constraint FK_ATIV_GRUPO_PROCED_REF_PROFISSIONAL foreign key (cd_profissional)
        references profissional (cd_profissional)
            on delete restrict on update restrict;

            alter table atividade_grupo_procedimento
	add constraint FK_ATIV_GRUPO_PROCED_REF_EMPRESA_BPA foreign key (empresa_bpa)
        references empresa (empresa)
            on delete restrict on update restrict;

/*
    Roger - 18/07/2017 - #15373
*/
ALTER TABLE tipo_procedimento ADD COLUMN flag_exibe_fila_espera_pub SMALLINT NOT NULL DEFAULT 0;
ALTER TABLE auditschema.tipo_procedimento ADD COLUMN flag_exibe_fila_espera_pub SMALLINT NULL;

/*
    Everton - 18/07/2017 - #15381
*/
update atividade_grupo_procedimento t1 set cd_profissional = (select min(cd_profissional) from atividade_grupo_profissional where cd_atv_grupo = t1.cd_atv_grupo and cd_cbo = t1.cd_cbo)
  where cd_cbo is not null and cd_profissional is null;

update atividade_grupo_procedimento t1 set empresa_bpa = (select coalesce(cd_empresa_bpa, empresa) from atividade_grupo where cd_atv_grupo = t1.cd_atv_grupo)
  where empresa_bpa is null;
alter table atividade_grupo_procedimento alter empresa_bpa set not null;



/*
        Maicon - 20/07/2017 - #15391
*/
alter table esus_marcadores_consumo_alimentar add column nr_atendimento INT8 null;
alter table auditschema.esus_marcadores_consumo_alimentar add column nr_atendimento INT8 null;

alter table esus_marcadores_consumo_alimentar
   add constraint FK_FICH_CONSUMO_ALIM_REF_ATEND foreign key (nr_atendimento)
      references atendimento (nr_atendimento);
