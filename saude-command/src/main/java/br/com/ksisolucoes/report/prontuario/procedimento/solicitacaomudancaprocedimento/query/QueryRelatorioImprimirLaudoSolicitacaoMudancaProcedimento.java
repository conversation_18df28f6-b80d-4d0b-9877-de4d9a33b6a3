package br.com.ksisolucoes.report.prontuario.procedimento.solicitacaomudancaprocedimento.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.prontuario.procedimento.solicitacaomudancaprocedimento.dto.RelatorioImprimirLaudoSolicitacaoMudancaProcedimentoDTO;
import br.com.ksisolucoes.vo.prontuario.procedimento.SolicitacaoMudancaProcedimento;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioImprimirLaudoSolicitacaoMudancaProcedimento extends CommandQuery<QueryRelatorioImprimirLaudoSolicitacaoMudancaProcedimento> implements ITransferDataReport<SolicitacaoMudancaProcedimento, RelatorioImprimirLaudoSolicitacaoMudancaProcedimentoDTO> {

    private SolicitacaoMudancaProcedimento param;
    private List<RelatorioImprimirLaudoSolicitacaoMudancaProcedimentoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("smp.codigo", "solicitacaoMudancaProcedimento.codigo");
        hql.addToSelect("smp.justificativa", "solicitacaoMudancaProcedimento.justificativa");
        hql.addToSelect("smp.dataSolicitacao", "solicitacaoMudancaProcedimento.dataSolicitacao");
        hql.addToSelect("smp.diagnosticoInicial", "solicitacaoMudancaProcedimento.diagnosticoInicial");
        
        hql.addToSelect("ucs.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("ucs.nome", "usuarioCadsus.nome");
        hql.addToSelect("ucs.sexo", "usuarioCadsus.sexo");
        hql.addToSelect("ucs.celular", "usuarioCadsus.celular");
        hql.addToSelect("ucs.telefone", "usuarioCadsus.telefone");
        hql.addToSelect("ucs.telefone2", "usuarioCadsus.telefone2");
        hql.addToSelect("ucs.nomeMae", "usuarioCadsus.nomeMae");
        hql.addToSelect("ucs.responsavel", "usuarioCadsus.responsavel");
        hql.addToSelect("ucs.dataNascimento", "usuarioCadsus.dataNascimento");
       
        hql.addToSelect("raca.descricao", "raca.descricao");
        
        hql.addToSelect("endereco.logradouro", "enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("endereco.numeroLogradouro", "enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("endereco.bairro", "enderecoUsuarioCadsus.bairro");
        hql.addToSelect("endereco.cep", "enderecoUsuarioCadsus.cep");

        hql.addToSelect("cidade.codigo", "cidade.codigo");
        hql.addToSelect("cidade.descricao", "cidade.descricao");
        
        hql.addToSelect("estado.sigla", "estado.sigla");

        hql.addToSelect("procAnt.codigo", "procedimentoAnterior.codigo");
        hql.addToSelect("procAnt.referencia", "procedimentoAnterior.referencia");
        hql.addToSelect("procAnt.descricao", "procedimentoAnterior.descricao");
        hql.addToSelect("procNovo.codigo", "procedimentoNovo.codigo");
        hql.addToSelect("procNovo.referencia", "procedimentoNovo.referencia");
        hql.addToSelect("procNovo.descricao", "procedimentoNovo.descricao");

        hql.addToSelect("cidPrincipal.codigo", "cidPrincipal.codigo");
        hql.addToSelect("cidPrincipal.descricao", "cidPrincipal.descricao");
        hql.addToSelect("cidSecundario.codigo", "cidSecundario.codigo");
        hql.addToSelect("cidSecundario.descricao", "cidSecundario.descricao");
        hql.addToSelect("cidCausasAssociadas.codigo", "cidCausasAssociadas.codigo");
        hql.addToSelect("cidCausasAssociadas.descricao", "cidCausasAssociadas.descricao");

        hql.addToSelect("profissional.nome", "profissional.nome");
        hql.addToSelect("profissional.codigoCns", "profissional.codigoCns");
        hql.addToSelect("profissional.cpf", "profissional.cpf");
        
        hql.addToSelect("aih.codigo", "aih.codigo");
        hql.addToSelect("aih.nroAutorizacao", "aih.nroAutorizacao");

        hql.setTypeSelect(RelatorioImprimirLaudoSolicitacaoMudancaProcedimentoDTO.class.getName());

        hql.addToFrom("SolicitacaoMudancaProcedimento smp "
                + " left join smp.atendimentoOrigem ao "
                + " left join smp.procedimentoAnterior procAnt "
                + " left join smp.novoProcedimento procNovo "
                + " left join smp.cidPrincipal cidPrincipal "
                + " left join smp.cidSecundario cidSecundario "
                + " left join smp.cidCausasAssociadas cidCausasAssociadas "
                + " left join smp.profissionalSolicitante profissional "
                + " left join smp.autorizacaoInternacaoHospitalar aih "
                + " left join ao.usuarioCadsus ucs "
                + " left join ucs.enderecoUsuarioCadsus endereco "
                + " left join ucs.raca raca "
                + " left join endereco.cidade cidade "
                + " left join cidade.estado estado ");

         hql.addToWhereWhithAnd("smp = ", param.getCodigo());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioImprimirLaudoSolicitacaoMudancaProcedimentoDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(SolicitacaoMudancaProcedimento param) {
        this.param = param;
    }
}
