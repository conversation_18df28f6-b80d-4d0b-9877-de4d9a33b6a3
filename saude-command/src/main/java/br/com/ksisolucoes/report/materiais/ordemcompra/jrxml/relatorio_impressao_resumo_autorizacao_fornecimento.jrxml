<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_impressao_resumo_autorizacao_fornecimento" printOrder="Horizontal" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="3338a750-9d07-4276-9464-b9351dbcc2b7">
	<property name="ireport.zoom" value="2.9282000000000266"/>
	<property name="ireport.x" value="267"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.RelatorioRelacaoOrdemCompraDTOParam.FormaApresentacao"/>
	<import value="br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.RelatorioRelacaoOrdemCompraDTOParam.BaseCalculo"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="FORMA_APRESENTACAO" class="br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.RelatorioRelacaoOrdemCompraDTOParam.FormaApresentacao"/>
	<parameter name="BASE_CALCULO" class="br.com.ksisolucoes.report.materiais.ordemcompra.interfaces.dto.RelatorioRelacaoOrdemCompraDTOParam.BaseCalculo"/>
	<field name="produto" class="br.com.ksisolucoes.vo.entradas.estoque.Produto"/>
	<field name="fornecedor" class="br.com.ksisolucoes.vo.basico.Pessoa"/>
	<field name="unidade" class="br.com.ksisolucoes.vo.entradas.estoque.Unidade"/>
	<field name="ordemCompra" class="br.com.ksisolucoes.vo.entradas.estoque.OrdemCompra"/>
	<field name="autorizacaoFornecimentoItem" class="br.com.ksisolucoes.vo.entradas.estoque.AutorizacaoFornecimentoItem"/>
	<field name="ordemCompraItem" class="br.com.ksisolucoes.vo.entradas.estoque.OrdemCompraItem"/>
	<field name="autorizacaoFornecimento" class="br.com.ksisolucoes.vo.entradas.estoque.AutorizacaoFornecimento"/>
	<field name="saldo" class="java.lang.Double"/>
	<variable name="totalValorFA" class="java.lang.Double" resetType="Group" resetGroup="formaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[]]></variableExpression>
	</variable>
	<variable name="totalGeral" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{saldo}]]></variableExpression>
	</variable>
	<variable name="totalResumo" class="java.lang.Double" resetType="Group" resetGroup="tipoResumo" calculation="Sum">
		<variableExpression><![CDATA[]]></variableExpression>
	</variable>
	<variable name="saldo" class="java.math.BigDecimal">
		<variableExpression><![CDATA[$F{autorizacaoFornecimentoItem}.getQtdItens().subtract($F{autorizacaoFornecimentoItem}.getQuantidadeRecebida())]]></variableExpression>
	</variable>
	<variable name="total" class="java.math.BigDecimal">
		<variableExpression><![CDATA[BaseCalculo.QUANTIDADE.value().equals($P{BASE_CALCULO}.value())
?
   $F{ordemCompraItem}.getQuantidadeCompra().multiply($F{ordemCompraItem}.getPrecoUnitario())
:
   $V{saldo}.multiply($F{ordemCompraItem}.getPrecoUnitario())]]></variableExpression>
	</variable>
	<variable name="totalFA" class="java.lang.Double" resetType="Group" resetGroup="formaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{saldo}]]></variableExpression>
	</variable>
	<variable name="totalQuantidade" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{autorizacaoFornecimentoItem}.getQtdItens()]]></variableExpression>
	</variable>
	<variable name="totalQuantidadeRecebida" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{autorizacaoFornecimentoItem}.getQuantidadeRecebida() != null
?
    $F{autorizacaoFornecimentoItem}.getQuantidadeRecebida()
:
0]]></variableExpression>
	</variable>
	<variable name="totalQuantidadeFA" class="java.lang.Double" resetType="Group" resetGroup="formaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{autorizacaoFornecimentoItem}.getQtdItens()]]></variableExpression>
	</variable>
	<variable name="totalQuantidadeRecebidaFA" class="java.lang.Double" resetType="Group" resetGroup="formaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{autorizacaoFornecimentoItem}.getQuantidadeRecebida() != null
?
    $F{autorizacaoFornecimentoItem}.getQuantidadeRecebida()
	:
0]]></variableExpression>
	</variable>
	<variable name="totalQuantidadePregao" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{autorizacaoFornecimentoItem}.getQtdItens()]]></variableExpression>
	</variable>
	<variable name="totalQuantidadeFAPregao" class="java.lang.Double" resetType="Group" resetGroup="formaApresentacao" calculation="Sum">
		<variableExpression><![CDATA[$F{ordemCompraItem}.getQuantidadePregao()]]></variableExpression>
	</variable>
	<group name="padrao">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupFooter>
			<band height="13">
				<textField>
					<reportElement x="290" y="1" width="57" height="12" uuid="a0e2b943-fb7c-4550-af98-7d8d21fb6085"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="286" y="0" width="269" height="1" uuid="3e9ae5e8-19e9-4d87-8265-80608df877dd"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="###0.00;-###0.00">
					<reportElement x="495" y="1" width="60" height="12" uuid="5796cd30-40b4-4a03-be53-678eb54dc4db"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalGeral}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00">
					<reportElement x="349" y="1" width="58" height="12" uuid="cf016073-4706-413f-90ff-bd7aac7fd16c"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalQuantidade}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00">
					<reportElement x="410" y="1" width="82" height="12" uuid="0d21fe8b-d472-4f31-938f-46af21da4da7"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalQuantidadeRecebida}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="formaApresentacao">
		<groupExpression><![CDATA[$F{ordemCompra}.getCodigo()+$F{produto}.getCodigo()]]></groupExpression>
		<groupHeader>
			<band height="28">
				<textField>
					<reportElement positionType="Float" x="0" y="1" width="55" height="12" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<box leftPadding="4"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_ordem_compra_abv")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="0" y="15" width="55" height="12" uuid="f6082c12-20a4-4fe5-9ee8-c3cadc7699b2"/>
					<box leftPadding="4"/>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="8"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ordemCompra}.getCodigo()]]></textFieldExpression>
				</textField>
				<line>
					<reportElement positionType="Float" x="0" y="14" width="555" height="1" uuid="7daaf6d7-15d0-44ee-b7c2-7b080ba3a973"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement positionType="Float" x="148" y="1" width="57" height="12" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="148" y="15" width="57" height="12" uuid="f6082c12-20a4-4fe5-9ee8-c3cadc7699b2"/>
					<textElement textAlignment="Center">
						<font fontName="Arial" size="8"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Data.formatar($F{ordemCompra}.getDataCadastro())]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="55" y="1" width="93" height="12" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_pregao")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="55" y="15" width="93" height="12" uuid="f6082c12-20a4-4fe5-9ee8-c3cadc7699b2"/>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="8"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ordemCompra}.getNumeroPregao()]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="206" y="1" width="122" height="12" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_fornecedor")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="206" y="15" width="122" height="12" uuid="f6082c12-20a4-4fe5-9ee8-c3cadc7699b2"/>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="8"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{fornecedor}.getDescricao()]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="329" y="1" width="153" height="12" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_produto")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement x="329" y="15" width="153" height="12" uuid="f6082c12-20a4-4fe5-9ee8-c3cadc7699b2"/>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="8"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{produto}.getDescricaoFormatado()]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="482" y="1" width="19" height="12" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement>
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_unidade_abv")]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="482" y="15" width="19" height="12" uuid="f6082c12-20a4-4fe5-9ee8-c3cadc7699b2"/>
					<textElement textAlignment="Left">
						<font fontName="Arial" size="8"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{unidade}.getUnidade()]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="501" y="1" width="54" height="12" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_qtd_pregao")]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement x="501" y="15" width="54" height="12" uuid="d782ea80-e2bf-4e45-b9a4-4c7892fa37d5"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{ordemCompraItem}.getQuantidadePregao()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="13">
				<textField pattern="###0.00;-###0.00">
					<reportElement x="495" y="1" width="60" height="12" uuid="dd1ac478-e9db-4151-857e-2b15380421d5"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalFA}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="306" y="1" width="41" height="12" uuid="347ee29f-ed4a-41c6-9801-623a09b604fe"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00">
					<reportElement x="349" y="1" width="58" height="12" uuid="dd1ac478-e9db-4151-857e-2b15380421d5"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalQuantidadeFA}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="314" y="0" width="241" height="1" uuid="7d77fcea-3f36-4617-b804-3ec4aa00f24a"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="###0.00;-###0.00">
					<reportElement x="410" y="1" width="82" height="12" uuid="bc6e9c20-a8cc-4f16-bff3-464d491c9d83"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalQuantidadeRecebidaFA}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="tipoResumo" isReprintHeaderOnEachPage="true">
		<groupHeader>
			<band height="14">
				<textField>
					<reportElement positionType="Float" x="349" y="1" width="58" height="12" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_qtd_af")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement positionType="Float" x="169" y="13" width="386" height="1" uuid="4a8d6dff-0b9d-490f-aa5b-9b31f385e91b"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement positionType="Float" x="410" y="1" width="82" height="12" uuid="58ba40b6-620c-4873-b37c-fd9d0288be7a"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade_recebia_abv_af")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="495" y="1" width="60" height="12" uuid="d5839070-b15a-4fa9-87ea-b150682d645e"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_saldo_af")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement positionType="Float" x="169" y="1" width="178" height="12" uuid="9c1d1860-2bf9-41ba-a03b-64dde8283b73"/>
					<box leftPadding="4"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_af")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement x="410" y="0" width="82" height="12" uuid="d782ea80-e2bf-4e45-b9a4-4c7892fa37d5"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{autorizacaoFornecimentoItem}.getQuantidadeRecebida() != null
?
    $F{autorizacaoFornecimentoItem}.getQuantidadeRecebida()
:
0]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement x="495" y="0" width="60" height="12" uuid="a9f5571d-2ca4-4d4b-805f-42f79bb14b4c"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{saldo}]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement x="349" y="0" width="58" height="12" uuid="5e3b76d4-5d57-43cc-a433-8133314ef15b"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{autorizacaoFornecimentoItem}.getQtdItens()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="169" y="0" width="178" height="12" uuid="61a11f9f-845f-4612-91d6-2a6833cd7348"/>
				<box leftPadding="4"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{autorizacaoFornecimento}.getNumeroAF() != null
?
    $F{autorizacaoFornecimento}.getNumeroAF()
:
    Bundle.getStringApplication("rotulo_nao_informado")]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band/>
	</pageFooter>
</jasperReport>
