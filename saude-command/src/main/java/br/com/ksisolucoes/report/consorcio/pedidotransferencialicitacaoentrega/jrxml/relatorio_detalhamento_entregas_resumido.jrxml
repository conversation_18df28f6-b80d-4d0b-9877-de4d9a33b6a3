<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_detalhamento_entregas" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="13187eaf-9762-4ee1-b075-9a13164bbb79">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="4.392300000000001"/>
	<property name="ireport.x" value="1597"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoEntregasDTOParam.FormaApresentacao"/>
	<import value="br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoEntregasDTOParam.TipoResumo"/>
	<import value="br.com.ksisolucoes.report.ReportProperties"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="formaApresentacao" class="br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoEntregasDTOParam.FormaApresentacao"/>
	<parameter name="tipoResumo" class="br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoEntregasDTOParam.TipoResumo"/>
	<field name="quantidade" class="java.lang.Long"/>
	<field name="formaApresentacao" class="java.lang.String"/>
	<field name="tipoResumo" class="java.lang.String"/>
	<field name="numeroGuia" class="java.lang.Long"/>
	<field name="dataEntrega" class="java.util.Date"/>
	<field name="descricaoConsorciado" class="java.lang.String"/>
	<field name="descricaoProduto" class="java.lang.String"/>
	<field name="valor" class="java.lang.Double"/>
	<field name="unidadeProduto" class="java.lang.String"/>
	<field name="somaValor" class="java.lang.Double"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="valorFA" class="java.lang.Double" resetType="Group" resetGroup="FA" calculation="Sum">
		<variableExpression><![CDATA[$F{valor}]]></variableExpression>
	</variable>
	<variable name="valorTotal" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{somaValor}]]></variableExpression>
	</variable>
	<variable name="valorTipoResumo" class="java.lang.Double" resetType="Group" resetGroup="TIPO_RESUMO" calculation="Sum">
		<variableExpression><![CDATA[$F{somaValor}]]></variableExpression>
	</variable>
	<variable name="valor_final" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{somaValor}]]></variableExpression>
	</variable>
	<group name="final">
		<groupFooter>
			<band height="14">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="399" y="2" width="96" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="1a6a1877-af3b-4217-85ea-71eb2c9d3546"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral")+":"]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="495" y="2" width="60" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="e2426471-675f-4409-b89d-864ba03b1af1"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valor_final}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="410" y="2" width="145" height="1" uuid="d3451f9c-8ee5-47bd-b193-75ca7b0af2f8"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="FA" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[$F{formaApresentacao}]]></groupExpression>
		<groupHeader>
			<band height="20">
				<rectangle radius="10">
					<reportElement mode="Transparent" x="0" y="7" width="555" height="13" uuid="909546be-2f75-479b-8995-0fed1c455549"/>
				</rectangle>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="0" y="7" width="555" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="9ce2519c-a610-4283-83c6-36e3f28e5ea8"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{formaApresentacao}.equals(FormaApresentacao.GUIA)
?
    Bundle.getStringApplication("rotulo_guia")+": "+$F{formaApresentacao}
:
    $F{formaApresentacao}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
	</group>
	<group name="TIPO_RESUMO" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[$F{tipoResumo}]]></groupExpression>
		<groupHeader>
			<band height="12">
				<printWhenExpression><![CDATA[! $P{formaApresentacao}.toString().equals($P{tipoResumo}.toString())]]></printWhenExpression>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="0" y="0" width="555" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="05620014-6b00-477c-bd17-d7fb45d2f08b"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="true" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{tipoResumo}.equals(TipoResumo.GUIA)
?
    Bundle.getStringApplication("rotulo_guia")+": "+$F{tipoResumo}
:
    $F{tipoResumo}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="14">
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="494" y="2" width="60" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="fb76b85a-b209-4bdc-a2bb-2c6c684c055c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorTipoResumo}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="398" y="2" width="96" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="31b86678-1434-4eff-a6bc-7b0d96ff97b3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")+
" "+$P{tipoResumo}.toString()+":"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="409" y="2" width="145" height="1" uuid="e023f5c0-8bec-45a5-8705-753fcbc33de6"/>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="CABECALHO" isReprintHeaderOnEachPage="true">
		<groupHeader>
			<band height="16">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="4" y="2" width="50" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="45c7e331-6638-4391-bdbc-f359a64ec663"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_n_guia")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="14" width="555" height="1" uuid="84be006e-ef7b-4fb5-b70c-13a0ccb0c732"/>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="94" y="2" width="76" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="875f9397-85df-42c3-a89c-e4c4be15328b"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="204" y="2" width="120" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="de5009b3-4f6d-45d5-996d-e4548d1b8610"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_consorciado")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="495" y="2" width="60" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="607687c8-cdcc-44f3-9968-d3b7f17364db"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_guia")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="4" y="0" width="50" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="3fcde699-27d4-4631-a317-d1cb4efae9dc"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{numeroGuia}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="204" y="0" width="256" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="a38762dc-7319-41b3-982e-d82dbf6a1b8d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoConsorciado}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH:mm" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="94" y="0" width="76" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="cf54bfd8-bd8e-43ca-871d-c28f7317b87d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataEntrega}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="495" y="0" width="60" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="58661f87-c3c0-4114-ad02-8d89063acc95"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{somaValor}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
