/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.integracao.bos.datasus.tabelascorporativas;

import br.com.ksisolucoes.integracao.bos.CommandTransaction;
import br.com.ksisolucoes.integracao.vos.interfaces.Entidade;
import br.com.ksisolucoes.integracao.dao.interfaces.DAO;
import br.com.ksisolucoes.integracao.dao.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class IntegrarInformacoesTabelasCorporativas extends CommandTransaction {

    private List<String> arquivos;

    public IntegrarInformacoesTabelasCorporativas(DAO dao, List<String> arquivos) {
        super(dao);
        this.arquivos = arquivos;
    }

    @Override
    public void execute() throws DAOException {
        Loggable.log.debug("************** Carregando ******************");
        CarregadorTabelasCorporativas carregador = new CarregadorTabelasCorporativas();
        carregador.setMapeamentos(arquivos);

        carregador.start();
        for (Entidade entidade : carregador.getRegistros()) {
            Loggable.log.debug("*********************************************");
            Loggable.log.debug(entidade.getNomeEntidade());
            for (Entidade.Atributo atributo : entidade.getAtributos()) {
                Loggable.log.debug(atributo.getNomeAtributo() + ": " + atributo.getValorAtributo().getValor());
            }

            Loggable.log.debug("************** Persistindo ******************");

            getDao().saveOrUpdate(entidade);
        }
        Loggable.log.debug("FIM");
    }

}
