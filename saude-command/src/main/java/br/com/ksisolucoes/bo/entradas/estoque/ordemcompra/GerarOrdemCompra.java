package br.com.ksisolucoes.bo.entradas.estoque.ordemcompra;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.QueryPedidoTransferenciaLicitacaoItensDTO;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.EstoqueEmpresaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacaoEloOrdemCompra;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacaoItem;
import br.com.ksisolucoes.vo.entradas.estoque.OrdemCompra;
import br.com.ksisolucoes.vo.entradas.estoque.OrdemCompraItem;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class GerarOrdemCompra extends AbstractCommandTransaction<GerarOrdemCompra> {

    private List<PedidoTransferenciaLicitacao> pedidoTransferenciaLicitacaoList;
    private String tipoAgrupamento;

    public GerarOrdemCompra(List<PedidoTransferenciaLicitacao> pedidoTransferenciaLicitacaoList) {
        this.pedidoTransferenciaLicitacaoList = pedidoTransferenciaLicitacaoList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (pedidoTransferenciaLicitacaoList == null || pedidoTransferenciaLicitacaoList.isEmpty()) {
            return;
        }

        tipoAgrupamento = BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("agrupamentoOrdemCompra");

        List<QueryPedidoTransferenciaLicitacaoItensDTO> pedidosPedidoTransferenciaLicitacaoItensDTOs
                = BOFactory.getBO(EstoqueEmpresaFacade.class).queryPedidoTransferenciaLicitacaoItens(pedidoTransferenciaLicitacaoList);

        OrdemCompra ordemCompra = null;
        OrdemCompraItem ordemCompraItem = null;
        for (QueryPedidoTransferenciaLicitacaoItensDTO dto : pedidosPedidoTransferenciaLicitacaoItensDTOs) {
            if (ordemCompra == null || !dto.getFornecedor().equals(ordemCompra.getPessoa()) ||
                    !dto.getNumeroPregao().equals(ordemCompra.getNumeroPregao()) ||
                    (ordemCompra.getPedidoTransferenciaLicitacao() != null
                            && !dto.getPedidoTransferenciaLicitacao().getCodigo().equals(ordemCompra.getPedidoTransferenciaLicitacao().getCodigo())
                            && RepositoryComponentDefault.PEDIDO.equals(tipoAgrupamento))) {

                ordemCompra = new OrdemCompra();
                ordemCompra.setPessoa(dto.getFornecedor());
                ordemCompra.setNumeroPregao(dto.getNumeroPregao());
                if (dto.getFundoConsorcio() != null && dto.getFundoConsorcio().getCodigo() != null) {
                    ordemCompra.setFundoConsorcio(dto.getFundoConsorcio());
                }

                if (RepositoryComponentDefault.PEDIDO.equals(tipoAgrupamento)) {
                    ordemCompra.setEmpresaConsorciado(dto.getEmpresaConsorciado());
                    ordemCompra.setPedidoTransferenciaLicitacao(dto.getPedidoTransferenciaLicitacao());
                    ordemCompra.setObservacao("Pedido: " + dto.getPedidoTransferenciaLicitacao().getCodigo() + ", Município: " + dto.getCidade().getDescricao());
                }
                BOFactory.save(ordemCompra);

                ordemCompraItem = null;
            }

            if ((ordemCompraItem == null) || !dto.getProduto().getCodigo().equals(ordemCompraItem.getProduto().getCodigo())) {
                ordemCompraItem = new OrdemCompraItem();
                ordemCompraItem.setOrdemCompra(ordemCompra);
                ordemCompraItem.setProduto(dto.getProduto());
                ordemCompraItem.setModelo(dto.getModelo());
                if (dto.getFabricante() != null && dto.getFabricante().getCodigo() != null) {
                    ordemCompraItem.setFabricante(dto.getFabricante());
                }
                ordemCompraItem.setDescricaoFabricante(dto.getFabricante() != null ? dto.getFabricante().getDescricao() : null);
                ordemCompraItem.setPrecoUnitario(BigDecimal.valueOf(dto.getPrecoUnitario()));
                ordemCompraItem.setNumeroItemPregao(dto.getNumeroItemPregao());
            }
            ordemCompraItem.setQuantidadeCompraOriginal(null); //Para atualizar a quantidade correta.
            ordemCompraItem.setQuantidadeCompra(BigDecimal.valueOf(new Dinheiro(Coalesce.asBigDecimal(ordemCompraItem.getQuantidadeCompra())).somar(Double.valueOf(dto.getQuantidadeCompra())).doubleValue()));
            ordemCompraItem.setQuantidadeCompraPedidoTransferencia(ordemCompraItem.getQuantidadeCompra());
            BOFactory.save(ordemCompraItem);

            PedidoTransferenciaLicitacaoItem pedidoTransferenciaLicitacaoItem = (PedidoTransferenciaLicitacaoItem) getSession().get(PedidoTransferenciaLicitacaoItem.class, dto.getPedidoTransferenciaLicitacaoItem().getCodigo());

            PedidoTransferenciaLicitacaoEloOrdemCompra pedidoTransferenciaLicitacaoEloOrdemCompra = new PedidoTransferenciaLicitacaoEloOrdemCompra();
            pedidoTransferenciaLicitacaoEloOrdemCompra.setOrdemCompraItem(ordemCompraItem);
            pedidoTransferenciaLicitacaoEloOrdemCompra.setPedidoTransferenciaLicitacaoItem(pedidoTransferenciaLicitacaoItem);
            pedidoTransferenciaLicitacaoEloOrdemCompra.setQuantidadeCompra(Double.valueOf(dto.getQuantidadeCompra()));
            BOFactory.save(pedidoTransferenciaLicitacaoEloOrdemCompra);
        }
        getSession().flush();
        getSession().clear();


        for (PedidoTransferenciaLicitacao pedidoTransferenciaLicitacao : pedidoTransferenciaLicitacaoList) {
            pedidoTransferenciaLicitacao.setFlagOrdemCompra(RepositoryComponentDefault.SIM_LONG);
            BOFactory.save(pedidoTransferenciaLicitacao);
        }

    }

}