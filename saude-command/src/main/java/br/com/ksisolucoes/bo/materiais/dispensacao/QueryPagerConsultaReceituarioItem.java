package br.com.ksisolucoes.bo.materiais.dispensacao;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.bo.materiais.dispensacao.interfaces.dto.QueryPagerConsultaReceituarioItemDTO;
import br.com.ksisolucoes.bo.materiais.dispensacao.interfaces.dto.QueryPagerConsultaReceituarioItemDTOparam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.Unidade;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerConsultaReceituarioItem extends CommandQueryPager<QueryPagerConsultaReceituarioItem> {

    private QueryPagerConsultaReceituarioItemDTOparam param;

    private final Long validadePrescricaoInterna;

    public QueryPagerConsultaReceituarioItem(QueryPagerConsultaReceituarioItemDTOparam param) throws DAOException {
        this.param = param;
        validadePrescricaoInterna = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("ValidadePrescricaoInterna");
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(QueryPagerConsultaReceituarioItemDTO.class.getName());

//        hql.addToSelect(new HQLProperties(Receituario.class, "receituario").getProperties());
        hql.addToSelect("receituarioItem.codigo", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_CODIGO));
        hql.addToSelect("receituarioItem.quantidadePrescrita", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_QUANTIDADE_PRESCRITA));
        hql.addToSelect("receituarioItem.quantidadeDispensada", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_QUANTIDADE_DISPENSADA));
        hql.addToSelect("receituario.codigo", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_CODIGO));
        hql.addToSelect("receituario.dataReceituario", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_DATA_RECEITUARIO));
        hql.addToSelect("receituario.dataCadastro", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_DATA_CADASTRO));
        hql.addToSelect("receituario.receitaContinua", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_RECEITA_CONTINUA));
        hql.addToSelect("atendimento.codigo", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO));
        hql.addToSelect("empresa.codigo", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_EMPRESA, Empresa.PROP_CODIGO));
        hql.addToSelect("empresa.descricao", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_EMPRESA, Empresa.PROP_DESCRICAO));
        hql.addToSelect("leitoQuarto.codigo", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_ATENDIMENTO, Atendimento.PROP_LEITO_QUARTO, LeitoQuarto.PROP_CODIGO));
        hql.addToSelect("leitoQuarto.descricao", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_ATENDIMENTO, Atendimento.PROP_LEITO_QUARTO, LeitoQuarto.PROP_DESCRICAO));
        hql.addToSelect("quartoInternacao.codigo", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_ATENDIMENTO, Atendimento.PROP_LEITO_QUARTO, LeitoQuarto.PROP_QUARTO_INTERNACAO, QuartoInternacao.PROP_CODIGO));
        hql.addToSelect("quartoInternacao.referencia", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_ATENDIMENTO, Atendimento.PROP_LEITO_QUARTO, LeitoQuarto.PROP_QUARTO_INTERNACAO, QuartoInternacao.PROP_REFERENCIA));
        hql.addToSelect("quartoInternacao.descricao", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_ATENDIMENTO, Atendimento.PROP_LEITO_QUARTO, LeitoQuarto.PROP_QUARTO_INTERNACAO, QuartoInternacao.PROP_DESCRICAO));
        hql.addToSelect("profissional.codigo", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_PROFISSIONAL, Profissional.PROP_CODIGO));
        hql.addToSelect("profissional.nome", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_PROFISSIONAL, Profissional.PROP_NOME));
        hql.addToSelect("produto.codigo", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_PRODUTO, Produto.PROP_CODIGO));
        hql.addToSelect("produto.descricao", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_PRODUTO, Produto.PROP_DESCRICAO));
        hql.addToSelect("unidade.codigo", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_CODIGO));
        hql.addToSelect("unidade.unidade", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE));
        hql.addToSelect("usuarioCadsus.codigo", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO));
        hql.addToSelect("usuarioCadsus.nome", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME));
        hql.addToSelect("usuarioCadsus.apelido", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO));
        hql.addToSelect("usuarioCadsus.utilizaNomeSocial", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL));
        hql.addToSelect("tipoReceita.codigo", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_TIPO_RECEITA, TipoReceita.PROP_CODIGO));
        hql.addToSelect("convenio.codigo", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_ATENDIMENTO, Atendimento.PROP_CONVENIO, Convenio.PROP_CODIGO));
        hql.addToSelect("empresaDispensacao.codigo", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_EMPRESA_DISPENSACAO, Empresa.PROP_CODIGO));
        hql.addToSelect("empresaDispensacao.descricao", VOUtils.montarPath(QueryPagerConsultaReceituarioItemDTO.PROP_RECEITUARIO_ITEM, ReceituarioItem.PROP_RECEITUARIO, Receituario.PROP_EMPRESA_DISPENSACAO, Empresa.PROP_DESCRICAO));
        hql.addToSelect("(receituarioItem.quantidadePrescrita - receituarioItem.quantidadeDispensada)", "saldo");


        hql.addToFrom("ReceituarioItem receituarioItem "
                + " left join receituarioItem.receituario receituario "
                + " left join receituarioItem.produto produto "
                + " left join produto.unidade unidade "
                + " left join receituario.atendimento atendimento "
                + " left join receituario.empresa empresa "
                + " left join atendimento.leitoQuarto leitoQuarto "
                + " left join leitoQuarto.quartoInternacao quartoInternacao "
                + " left join receituario.profissional profissional "
                + " left join receituario.usuarioCadsus usuarioCadsus "
                + " left join receituario.tipoReceita tipoReceita"
                + " left join receituario.empresaDispensacao empresaDispensacao"
                + " left join atendimento.convenio convenio"
        );

        hql.addToWhereWhithAnd("atendimento.codigo = ", param.getNumeroAtendimento());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("usuarioCadsus.nome", param.getNomePaciente()));
        hql.addToWhereWhithAnd("produto = ", param.getProduto());
        hql.addToWhereWhithAnd("quartoInternacao.referencia = ", param.getQuarto());

        hql.addToWhereWhithAnd("receituario.situacao != ", Receituario.Situacao.CANCELADO.value());
        hql.addToWhereWhithAnd("receituarioItem.status <> ", ReceituarioItem.Status.CANCELADO.value());

        if(param.getEmpresaDispensacao() != null) {
            hql.addToWhereWhithAnd("empresaDispensacao.codigo = ", param.getEmpresaDispensacao().getCodigo());
        } else if(param.isPermissaoVisualizarApenasPrescricaoEstabelecimento()) {
            try {
                Usuario usuario = SessaoAplicacaoImp.getInstance().<br.com.ksisolucoes.vo.controle.Usuario>getUsuario();
                if (!usuario.isNivelAdminOrMaster()) {
                    usuario.setEmpresasUsuario(BOFactory.getBO(UsuarioFacade.class).getEmpresasUsuario(usuario));
                    hql.addToWhereWhithAnd("empresaDispensacao.codigo in ", usuario.getEmpresasUsuario());
                }
            } catch (SGKException ex) {
                Loggable.log.error(ex);
            }
        }

        hql.addToWhereWhithAnd("((Coalesce(receituarioItem.quantidadePrescrita, 0) - Coalesce(receituarioItem.quantidadeDispensada, 0)) > 0"
                + "or "
                + "EXISTS(select 1 from ReceituarioItemComponente as ric where ric.receituarioItem = receituarioItem.codigo "
                + " and (Coalesce(ric.quantidadePrescrita, 0) - Coalesce(ric.quantidadeDispensada, 0)) > 0))");

        /**
         * A data de inicio para verificacao da validade sera a data atual,
         * reduzindo o parametro gem da validade (dado em horas), qualquer data
         * da prescricao contida desde o inicio deste dia
         */
        Date dataInicioValidade = Data.adjustRangeHour(Data.removeMinutos(DataUtil.getDataAtual(), (validadePrescricaoInterna.intValue() * 60))).getDataInicial();
        hql.addToWhereWhithAnd("receituario.dataReceituario >=", dataInicioValidade);

        if (param.getPropSort() != null) {
            hql.addToOrder(param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
            if (param.getPropSort().equals("leitoQuarto.descricao")) {
                hql.addToOrder("quartoInternacao.referencia" + " " + (param.isAscending() ? "asc" : "desc"));
            }
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
