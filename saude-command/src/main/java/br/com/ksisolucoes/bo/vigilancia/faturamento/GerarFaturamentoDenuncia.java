package br.com.ksisolucoes.bo.vigilancia.faturamento;

import br.com.celk.vigilancia.helper.FaturamentoVigilanciaHelper;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.LancamentoAtividadesVigilanciaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;
import br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilanciaItem;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaAtividades;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class GerarFaturamentoDenuncia extends AbstractCommandTransaction {

    private boolean cadastro;
    private RequerimentoVigilancia requerimentoVigilancia;
    private LancamentoAtividadesVigilanciaDTO lancamentoAtividadesVigilanciaDTO;

    public GerarFaturamentoDenuncia(RequerimentoVigilancia requerimentoVigilancia, boolean cadastro) {
        this.requerimentoVigilancia = requerimentoVigilancia;
        this.cadastro = cadastro;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (cadastro) {
            FaturamentoVigilanciaHelper.validarConfiguracaoVigilanciaAtividades(ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DENUNCIA_CADASTRO);
        } else {
            FaturamentoVigilanciaHelper.validarConfiguracaoVigilanciaAtividades(ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DENUNCIA_FINALIZACAO);
        }

        Denuncia denuncia = LoadManager.getInstance(Denuncia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Denuncia.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                .setMaxResults(1).start().getVO();

        if (denuncia == null && !cadastro) {
            throw new ValidacaoException("Não foi encontrado um registro de denúncia para o requerimento " + requerimentoVigilancia.getProtocoloFormatado());
        }

        List<Profissional> profissionalList = FaturamentoVigilanciaHelper.getProfissionalList(requerimentoVigilancia);
        if (CollectionUtils.isNotNullEmpty(profissionalList)) {
            for (Profissional profissional : profissionalList) {
                lancamentoAtividadesVigilanciaDTO = new LancamentoAtividadesVigilanciaDTO();

                LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia = gerarLancamentoAtividadeVigilancia(profissional, requerimentoVigilancia.getEstabelecimento(), requerimentoVigilancia.getVigilanciaPessoa(), requerimentoVigilancia);

                List<LancamentoAtividadesVigilanciaItem> lancamentoAtividadesVigilanciaItemList = new ArrayList();

                gerarAtividadePadrao(lancamentoAtividadesVigilancia, lancamentoAtividadesVigilanciaItemList);

                lancamentoAtividadesVigilanciaDTO.setFaturavel(false); //não gera item de conta (BPA)
                lancamentoAtividadesVigilanciaDTO.setLancamentoAtividadesVigilancia(lancamentoAtividadesVigilancia);
                lancamentoAtividadesVigilanciaDTO.setLancamentoAtividadesVigilanciaItemList(lancamentoAtividadesVigilanciaItemList);
                BOFactory.getBO(VigilanciaFacade.class).salvarLancamentoAtividadesVigilancia(lancamentoAtividadesVigilanciaDTO);
            }
        }
    }

    private void gerarAtividadePadrao(LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia, List<LancamentoAtividadesVigilanciaItem> lancamentoAtividadesVigilanciaItemList) throws DAOException {

        LancamentoAtividadesVigilanciaItem lancamentoAtividadesVigilanciaItem = new LancamentoAtividadesVigilanciaItem();
        AtividadesVigilancia atividadesVigilanciaDefault = null;

        if (cadastro) {
            atividadesVigilanciaDefault = FaturamentoVigilanciaHelper.getAtividadeVigilanciaDefault(ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DENUNCIA_CADASTRO);
        } else {
            atividadesVigilanciaDefault = FaturamentoVigilanciaHelper.getAtividadeVigilanciaDefault(ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DENUNCIA_FINALIZACAO);
        }

        lancamentoAtividadesVigilanciaItem.setAtividadesVigilancia(atividadesVigilanciaDefault);
        lancamentoAtividadesVigilanciaItem.setPontuacao(atividadesVigilanciaDefault.getPontuacao());
        lancamentoAtividadesVigilanciaItem.setLancamentoAtividadesVigilancia(lancamentoAtividadesVigilancia);
        lancamentoAtividadesVigilanciaItem.setQuantidade(1L);
        lancamentoAtividadesVigilanciaItemList.add(lancamentoAtividadesVigilanciaItem);
    }

    private LancamentoAtividadesVigilancia gerarLancamentoAtividadeVigilancia(Profissional profissional, Estabelecimento estabelecimento, VigilanciaPessoa vigilanciaPessoa, RequerimentoVigilancia requerimentoVigilancia) {
        LancamentoAtividadesVigilancia lancamentoAtividadesVigilancia = new LancamentoAtividadesVigilancia();
        lancamentoAtividadesVigilancia.setProfissional(profissional);

        if (cadastro) {
            lancamentoAtividadesVigilancia.setTipoAtividade(ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DENUNCIA_CADASTRO.descricao());
            lancamentoAtividadesVigilancia.setDataAtividade(requerimentoVigilancia.getDataRequerimento());
        } else {
            lancamentoAtividadesVigilancia.setTipoAtividade(ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.DENUNCIA_FINALIZACAO.descricao());
            lancamentoAtividadesVigilancia.setDataAtividade(requerimentoVigilancia.getDataFinalizacao());
        }

        lancamentoAtividadesVigilancia.setRequerimentoVigilancia(requerimentoVigilancia);
        lancamentoAtividadesVigilancia.setEstabelecimento(estabelecimento);
        lancamentoAtividadesVigilancia.setVigilanciaPessoa(vigilanciaPessoa);
        lancamentoAtividadesVigilancia.setCnpjCpfPessoa(estabelecimento != null && estabelecimento.getCodigo() != null ? estabelecimento.getCnpjCpf(): vigilanciaPessoa != null ? vigilanciaPessoa.getCpf() : null);
        lancamentoAtividadesVigilancia.setAtividadeEstabelecimento(estabelecimento != null ? FaturamentoVigilanciaHelper.getAtividadePrincipalEstabelecimento(estabelecimento) : FaturamentoVigilanciaHelper.getAtividadeVigilanciaPessoa(vigilanciaPessoa));
        lancamentoAtividadesVigilancia.setFlagTipo(estabelecimento != null  ? LancamentoAtividadesVigilancia.TipoPessoa.ESTABELECIMENTO.value() : LancamentoAtividadesVigilancia.TipoPessoa.PESSOA.value());

        return lancamentoAtividadesVigilancia;
    }
}
