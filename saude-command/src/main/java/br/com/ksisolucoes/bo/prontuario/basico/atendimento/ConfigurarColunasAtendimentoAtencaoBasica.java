package br.com.ksisolucoes.bo.prontuario.basico.atendimento;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.CadastroConfiguracaoAtendimentoAtencaoBasicaDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.ConfiguracaoAtendimentoAtencaoBasicaDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.ConfiguracaoOrdenacaoAtendimentoAtencaoBasicaDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ConfiguracaoAtendimentoBasica;
import br.com.ksisolucoes.vo.prontuario.basico.ConfiguracaoOrdenacaoAtendimentoBasica;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;

import java.util.List;

import static ch.lambdaj.Lambda.by;
import static ch.lambdaj.Lambda.on;

/**
 * Created by sulivan on 17/01/19.
 */
public class ConfigurarColunasAtendimentoAtencaoBasica extends AbstractCommandTransaction {

    private CadastroConfiguracaoAtendimentoAtencaoBasicaDTO dto;

    public ConfigurarColunasAtendimentoAtencaoBasica(CadastroConfiguracaoAtendimentoAtencaoBasicaDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        // Configurações de posição de colunas
        removerConfiguracoesExistentes();
        Group<ConfiguracaoAtendimentoAtencaoBasicaDTO> group = Lambda.group(dto.getConfiguracaoList(), by(on(ConfiguracaoAtendimentoAtencaoBasicaDTO.class).getConfiguracao().getEmpresa()));

        Long i;
        ConfiguracaoAtendimentoBasica config;

        for (Group<ConfiguracaoAtendimentoAtencaoBasicaDTO> sub : group.subgroups()) {
            i = 1L;
            for (ConfiguracaoAtendimentoAtencaoBasicaDTO item : sub.findAll()) {
                config = new ConfiguracaoAtendimentoBasica();
                config.setCodigoColuna(item.getConfiguracao().getCodigoColuna());
                config.setOrdem(i);
                config.setEmpresa(item.getConfiguracao().getEmpresa());

                BOFactory.save(config);
                i++;
            }
        }

        // Configurações de ordenação de colunas
        removerConfiguracoesOrdenacaoExistentes();

        Group<ConfiguracaoOrdenacaoAtendimentoAtencaoBasicaDTO> groupSorter = Lambda.group(dto.getConfiguracaoOrdenacaoList(), by(on(ConfiguracaoOrdenacaoAtendimentoAtencaoBasicaDTO.class)
                .getConfiguracaoOrdenacao().getEmpresa()));

        ConfiguracaoOrdenacaoAtendimentoBasica configSorter;

        for (Group<ConfiguracaoOrdenacaoAtendimentoAtencaoBasicaDTO> sub : groupSorter.subgroups()) {
            i = 1L;
            for (ConfiguracaoOrdenacaoAtendimentoAtencaoBasicaDTO item : sub.findAll()) {
                configSorter = new ConfiguracaoOrdenacaoAtendimentoBasica();
                configSorter.setCodigoColuna(item.getConfiguracaoOrdenacao().getCodigoColuna());
                configSorter.setOrdem(i);
                configSorter.setEmpresa(item.getConfiguracaoOrdenacao().getEmpresa());
                configSorter.setTipo(item.getConfiguracaoOrdenacao().getTipo());

                BOFactory.save(configSorter);
                i++;
            }
        }
    }

    private void removerConfiguracoesExistentes() throws DAOException, ValidacaoException {
        List<ConfiguracaoAtendimentoBasica> itensExistentes = LoadManager.getInstance(ConfiguracaoAtendimentoBasica.class)
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(itensExistentes)) {
            for (ConfiguracaoAtendimentoBasica configuracao : itensExistentes) {
                BOFactory.delete(configuracao);
            }
        }
    }

    private void removerConfiguracoesOrdenacaoExistentes() throws DAOException, ValidacaoException {
        List<ConfiguracaoOrdenacaoAtendimentoBasica> itensExistentes = LoadManager.getInstance(ConfiguracaoOrdenacaoAtendimentoBasica.class)
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(itensExistentes)) {
            for (ConfiguracaoOrdenacaoAtendimentoBasica configuracaoOrdenacao : itensExistentes) {
                BOFactory.delete(configuracaoOrdenacao);
            }
        }
    }
}