package br.com.ksisolucoes.bo.consorcio.consorcioprovisaoorcamentaria;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.QueryConsultaProvisaoOrcamentariaDTO;
import br.com.ksisolucoes.bo.consorcio.interfaces.dto.QueryConsultaProvisaoOrcamentariaDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaProvisaoOrcamentaria extends CommandQueryPager<QueryConsultaProvisaoOrcamentaria> {

    private QueryConsultaProvisaoOrcamentariaDTOParam param;

    public QueryConsultaProvisaoOrcamentaria(QueryConsultaProvisaoOrcamentariaDTOParam param) {
        this.param = param;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelectAndGroup("consorciado.codigo", "consorciado.codigo");
        hql.addToSelectAndGroup("consorciado.descricao", "consorciado.descricao");
        hql.addToSelectAndGroup("empresaConsorcio.codigo", "consorcio.codigo");
        hql.addToSelectAndGroup("empresaConsorcio.descricao", "consorcio.descricao");
        
        hql.setTypeSelect(QueryConsultaProvisaoOrcamentariaDTO.class.getName());
        hql.addToFrom("ConsorcioProvisaoOrcamentaria consorcioProvisaoOrcamentaria"
                + " left join consorcioProvisaoOrcamentaria.empresaConsorcio empresaConsorcio"
                + " left join consorcioProvisaoOrcamentaria.subConta subConta"
                + " left join subConta.conta conta"
                + " left join conta.consorciado consorciado");
        
        hql.addToWhereWhithAnd("empresaConsorcio =", param.getConsorcio());
        hql.addToWhereWhithAnd("consorciado =", param.getConsorciado());
        
        if (param.getSortProp().equals(QueryConsultaProvisaoOrcamentariaDTO.PROP_CONSORCIO)) {
            hql.addToOrder("empresaConsorcio.descricao " + (param.isAscending()?"asc":"desc"));
        } else if (param.getSortProp().equals(QueryConsultaProvisaoOrcamentariaDTO.PROP_CONSORCIADO)) {
            hql.addToOrder("consorciado.descricao " + (param.isAscending()?"asc":"desc"));
        }
        
    }

    @Override
    protected HQLHelper customHQLCount(HQLHelper hql) {
        hql = new HQLHelper();
        hql.setSelect("count(distinct cast(consorcio.empresa as varchar) ||'-'|| cast(consorciado.empresa as varchar))");
        hql.setFrom("consorcio_provisao_orcamentaria cpo"
                + " left join empresa consorcio on cpo.empresa_consorcio = consorcio.empresa"
                + " left join subconta subconta on cpo.cd_subconta = subconta.cd_subconta"
                + " left join conta conta on subconta.cd_conta = conta.cd_conta"
                + " left join empresa consorciado on conta.empresa = consorciado.empresa");
        if (param.getConsorciado()!=null) {
            hql.addToWhereWhithAnd("consorciado.empresa =", param.getConsorciado().getCodigo());
        }
        if (param.getConsorcio()!=null) {
            hql.addToWhereWhithAnd("consorcio.empresa =", param.getConsorcio().getCodigo());
        }

        hql.setUseSQL(true);
        
        return hql;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>)result);
    }

}
