package br.com.ksisolucoes.bo.agendamento;

import br.com.celk.bo.service.sms.interfaces.facade.SmsFacade;
import br.com.celk.whatsapp.WhatsAppHelper;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.service.sms.QueryConsultaAgendamentosSms;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ProcessoEnvioSMS extends AbstractCommandTransaction<ProcessoEnvioSMS> {

    private int numeroSMSEnviados = 0;

    @Override
    public void execute() throws DAOException, ValidacaoException {

        QueryConsultaAgendamentosSms query = new QueryConsultaAgendamentosSms();
        query.start();

        List<AgendaGradeAtendimentoHorario> listAgendamentos = new ArrayList<AgendaGradeAtendimentoHorario>(query.getResult());

        try {
            if (CollectionUtils.isNotNullEmpty(listAgendamentos)) {
                Loggable.log.info("Envio dos Agendamentos pela Solicitação");
                BOFactory.getBO(AgendamentoFacade.class).processarSmsAgendaGradeAtendimentoHorario(listAgendamentos);
                this.numeroSMSEnviados = listAgendamentos.size();
                if (!WhatsAppHelper.isPlataformaWhatsApp()) {
                    Loggable.log.info("Enviando " + this.numeroSMSEnviados + " mensagens.");
                }
            } else {
                Loggable.log.warn(Bundle.getStringApplication("msg_sem_registros_mensagens_para_enviar"));
            }
        } catch (Throwable ex) {
            Loggable.log.error(Bundle.getStringApplication("msg_erro_enviar_sms") + " : " + ex.getMessage());
            BOFactory.getBO(SmsFacade.class).enviarEmailErroSms(ex);
        }
    }

    public int getNumeroSMSEnviados() {
        return numeroSMSEnviados;
    }
}
