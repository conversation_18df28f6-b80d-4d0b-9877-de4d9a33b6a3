package br.com.celk.esus;

import br.com.celk.bo.esus.interfaces.facade.EsusFacade;
import br.com.celk.bo.hospital.endereco.EnderecoHelper;
import br.com.celk.helper.IntegracaoCdsHelper;
import br.com.celk.unidadesaude.CiapHelper;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.QueryConsultaEsusIntegracaoCdsAtendimentoIndividualDTO;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.QueryConsultaEsusIntegracaoCdsAtendimentoIndividualDTOParam;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.CnsValidator;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItem;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItemProcedimento;
import br.com.ksisolucoes.vo.esus.CboFichaEsus;
import br.com.ksisolucoes.vo.esus.CboFichaEsusItem;
import br.com.ksisolucoes.vo.esus.EsusIntegracaoCds;
import br.com.ksisolucoes.vo.esus.dto.EsusValidacoesFichasDTOParam;
import br.com.ksisolucoes.vo.esus.helper.EsusValidacoesFichaAtendimentoIndividualHelper;
import br.com.ksisolucoes.vo.esus.helper.EsusValidacoesFichaAtendimentoIndividualItemHelper;
import br.com.ksisolucoes.vo.esus.helper.EsusValidacoesFichaAtendimentoIndividualItemProcedimentoHelper;
import br.com.ksisolucoes.vo.prontuario.basico.Ciap;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;


import br.gov.saude.esus.cds.transport.generated.thrift.atendindividual.ExameThrift;
import br.gov.saude.esus.cds.transport.generated.thrift.atendindividual.FichaAtendimentoIndividualChildThrift;
import br.gov.saude.esus.cds.transport.generated.thrift.atendindividual.FichaAtendimentoIndividualMasterThrift;
import br.gov.saude.esus.cds.transport.generated.thrift.atendindividual.ProblemaCondicaoAvaliacaoAIThrift;
import br.gov.saude.esus.cds.transport.generated.thrift.common.LotacaoHeaderThrift;
import br.gov.saude.esus.cds.transport.generated.thrift.common.VariasLotacoesHeaderThrift;
import br.gov.saude.esus.transport.common.generated.thrift.DadoTransporteThrift;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.thrift.TException;
import org.hamcrest.Matchers;

import java.util.*;

import static ch.lambdaj.Lambda.on;

/**
 * Created by sulivan on 20/07/17.
 */
public class GerarArquivoEsusIntegracaoCdsAtendimentoIndividual extends GerarArquivoEsusIntegracaoCds implements IExportacaoEsusDetalhado {

    public GerarArquivoEsusIntegracaoCdsAtendimentoIndividual(ExportacaoEsusDTOParam param) {
        this.param = param;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Empresa unidadeCentral = getCentralCareUnit();

        int first = 0;
        int limit = 500;
        int retorno;
        EsusValidacoesFichasDTOParam paramValidacao = new EsusValidacoesFichasDTOParam();
        paramValidacao.setRetorno(EsusValidacoesFichasDTOParam.Retorno.INCONSISTENCIA);
        List<CboFichaEsusItem> cboFichaEsusItemList = getCboFichaEsusItems(CboFichaEsus.TipoFicha.FICHA_ATENDIMENTO_INDIVIDUAL.value());

        paramValidacao.setCboFichaEsusItemList(cboFichaEsusItemList);

        try {
            do {
                IntegracaoCdsHelper.flushClearSession(getSession());
                retorno = gerarArquivo(first, limit, unidadeCentral, paramValidacao);
                Loggable.log.info("registro numero " + (first + retorno) + " processado (Atendimentos)");
                first += limit;
            } while (retorno >= limit);
        } catch (ValidacaoException ex) {
            addInconsistency(ExceptionUtils.getMessage(ex));
        } catch (Exception ex) {
            addInconsistency(ExceptionUtils.getStackTrace(ex));
        }
    }

    private int gerarArquivo(int first, int limit, Empresa unidadeCentral, EsusValidacoesFichasDTOParam paramValidacao) throws DAOException, ValidacaoException {
        QueryConsultaEsusIntegracaoCdsAtendimentoIndividualDTOParam queryParam = new QueryConsultaEsusIntegracaoCdsAtendimentoIndividualDTOParam();
        queryParam.setPeriodo(param.getPeriodo());
        queryParam.setFirst(first);
        queryParam.setLimit(limit);

        List<QueryConsultaEsusIntegracaoCdsAtendimentoIndividualDTO> registros = BOFactory.getBO(EsusFacade.class).queryConsultaEsusIntegracaoCdsAtendimentoIndividual(queryParam);

        if (CollectionUtils.isNotNullEmpty(registros)) {
            List<EsusFichaAtendIndividualItem> atendimentos = new ArrayList<EsusFichaAtendIndividualItem>();
            List<FichaAtendimentoIndividualChildThrift> lstFaict = new ArrayList();
            List<EsusFichaAtendIndividualItemProcedimento> outrosSiaList;
            List<EsusFichaAtendIndividualItemProcedimento> examesEsusSolicitadosAvaliadosList;
            List<EsusIntegracaoCds> esusIntegracaoCdsList;
            FichaAtendimentoIndividualMasterThrift faim;
            FichaAtendimentoIndividualChildThrift faict;
            Profissional profissional;
            Empresa empresa;
            TabelaCbo tabelaCbo;
            String inconsistencia;

            Group<QueryConsultaEsusIntegracaoCdsAtendimentoIndividualDTO> byFicha = Lambda.group(registros, Lambda.by(Lambda.on(QueryConsultaEsusIntegracaoCdsAtendimentoIndividualDTO.class).getEsusIntegracaoCds().getEsusFichaAtendIndividual().getCodigo()));

            forPrincipal:
            for (Group<QueryConsultaEsusIntegracaoCdsAtendimentoIndividualDTO> subgroup : byFicha.subgroups()) {
                QueryConsultaEsusIntegracaoCdsAtendimentoIndividualDTO dto = subgroup.first();

                List<QueryConsultaEsusIntegracaoCdsAtendimentoIndividualDTO> itens = subgroup.findAll();
                esusIntegracaoCdsList = new ArrayList();
                esusIntegracaoCdsList.addAll(Lambda.extract(itens, Lambda.on(QueryConsultaEsusIntegracaoCdsAtendimentoIndividualDTO.class).getEsusIntegracaoCds()));

                empresa = dto.getEsusIntegracaoCds().getEsusFichaAtendIndividual().getEmpresa();
                profissional = dto.getEsusIntegracaoCds().getEsusFichaAtendIndividual().getProfissionalPrincipal();
                tabelaCbo = dto.getEsusIntegracaoCds().getEsusFichaAtendIndividual().getCboPrincipal();

                paramValidacao.setEmpresa(empresa);
                paramValidacao.setProfissional(profissional);
                paramValidacao.setTabelaCbo(tabelaCbo);
                paramValidacao.setEsusFichaAtendIndividual(dto.getEsusIntegracaoCds().getEsusFichaAtendIndividual());

                inconsistencia = EsusValidacoesFichaAtendimentoIndividualHelper.executarValidacoesComuns(paramValidacao);

                if (inconsistencia != null) {
                    addInconsistency(inconsistencia, esusIntegracaoCdsList);
                    continue;
                }

                atendimentos = new ArrayList();
                lstFaict = new ArrayList();

                for (EsusFichaAtendIndividualItem item : carregarFichasEsusItem(dto.getEsusIntegracaoCds().getEsusFichaAtendIndividual().getCodigo(), dto.getItemList())) {
                    QueryConsultaEsusIntegracaoCdsAtendimentoIndividualDTO select =
                            Lambda.selectFirst(itens,
                                    Lambda.having(Lambda.on(QueryConsultaEsusIntegracaoCdsAtendimentoIndividualDTO.class).getEsusIntegracaoCds().getEsusFichaAtendIndividualItem().getCodigo(),
                                            Matchers.equalTo(item.getCodigo())));

                    EsusIntegracaoCds esusIntegracaoCds = select.getEsusIntegracaoCds();

                    paramValidacao.setEsusFichaAtendIndividualItem(item);
                    paramValidacao.setCiapValido(isCiapValido(item.getCiap()));
                    paramValidacao.setCiap2Valido(isCiapValido(item.getCiap2()));

                    inconsistencia = EsusValidacoesFichaAtendimentoIndividualItemHelper.executarValidacoesComuns(paramValidacao);

                    if (inconsistencia != null) {
                        addInconsistency(inconsistencia, Arrays.asList(esusIntegracaoCds));
                        continue forPrincipal;
                    }

                    faict = new FichaAtendimentoIndividualChildThrift();
                    faict.setNumeroProntuario(item.getNumeroProntuario());
                    faict.setCpfCidadao(item.getUsuarioCadsus().getCpf());

                    if (item.getNumeroCartao() != null
                            && CnsValidator.validaCns(item.getNumeroCartao().toString())
                            && (faict.getCpfCidadao() == null || faict.getCpfCidadao().isEmpty())) {
                        faict.setCns(item.getNumeroCartao().toString());
                    }

                    faict.setDataNascimento(item.getDataNascimento().getTime());

                    Date dataAtendimentoInicial = item.getEsusFichaAtendIndividual().getDataAtendimento();
                    Date dataAtendimentoFinal = item.getEsusFichaAtendIndividual().getDataAtendimentoFinal();
                    faict.setDataHoraInicialAtendimento(dataAtendimentoInicial.getTime());
                    faict.setDataHoraFinalAtendimento(dataAtendimentoFinal != null ? dataAtendimentoFinal.getTime() : dataAtendimentoInicial.getTime());

                    faict.setLocalDeAtendimento(item.getLocalAtendimento());
                    faict.setSexo(item.getSexo());
                    if (item.getTurno() != null) {
                        faict.setTurno(item.getTurno());
                    }
                    faict.setTipoAtendimento(item.getTipoAtendimento());
                    if (item.getPeso() != null) {
                        faict.setPesoAcompanhamentoNutricional(item.getPeso());
                    }
                    if (item.getAltura() != null) {
                        faict.setAlturaAcompanhamentoNutricional(item.getAltura());
                    }
                    if (item.getAleitamentoMaterno() != null) {
                        faict.setAleitamentoMaterno(item.getAleitamentoMaterno());
                    }
                    if (item.getDumGestante() != null) {
                        faict.setDumDaGestante(item.getDumGestante().getTime());
                    }
                    if (item.getIdadeGestacional() != null) {
                        faict.setIdadeGestacional(item.getIdadeGestacional().intValue());
                    }
                    if (item.getAtencaoDomiciliar() != null) {
                        faict.setAtencaoDomiciliarModalidade(item.getAtencaoDomiciliar());
                    }
                    faict.setProblemaCondicaoAvaliada(getProblemaCondicaoAvaliada(item));

                    faict.setVacinaEmDia(RepositoryComponentDefault.SIM_LONG.equals(item.getVacinaEmDia()));

                    examesEsusSolicitadosAvaliadosList = carregarFichasEsusItemProcedimento(item.getCodigo(), dto.getItemProcedimentoList());

                    inconsistencia = validarExamesEsusAvaliadosSolicitados(examesEsusSolicitadosAvaliadosList, paramValidacao);

                    if (inconsistencia != null && !inconsistencia.isEmpty()) {
                        addInconsistency(inconsistencia, Arrays.asList(esusIntegracaoCds));
                        continue forPrincipal;
                    }

                    outrosSiaList = carregarFichasEsusItemProcedimento(item.getCodigo(), dto.getItemProcedimentoList());

                    inconsistencia = validarOutrosSia(outrosSiaList, paramValidacao);

                    if (inconsistencia != null && !inconsistencia.isEmpty()) {
                        addInconsistency(inconsistencia, Arrays.asList(esusIntegracaoCds));
                        continue forPrincipal;
                    }

                    List<ExameThrift> exameThriftList = new ArrayList<>();
                    ExameThrift et;
                    List<String> solicitadoList;
                    List<String> exameSolicitadoList = getExameSolicitado(carregarFichasEsusItemProcedimento(item.getCodigo(), dto.getItemProcedimentoList()));
                    List<String> exameAvaliadoList = getExameAvaliado(carregarFichasEsusItemProcedimento(item.getCodigo(), dto.getItemProcedimentoList()));
                    if (br.com.celk.util.CollectionUtils.isNotNullEmpty(exameSolicitadoList)) {
                        for (String codigoExame : exameSolicitadoList) {
                            et = new ExameThrift();
                            solicitadoList = new ArrayList<>();
                            if (exameAvaliadoList.contains(codigoExame)) {
                                solicitadoList.add("A");
                            }
                            solicitadoList.add("S");
                            et.setCodigoExame(codigoExame);
                            et.setSolicitadoAvaliado(solicitadoList);
                            et.setCodigoExameIsSet(true);
                            et.setSolicitadoAvaliadoIsSet(true);
                            exameThriftList.add(et);
                        }
                    }

                    if (br.com.celk.util.CollectionUtils.isNotNullEmpty(exameAvaliadoList)) {
                        for (String codigoExame : exameAvaliadoList) {
                            if (br.com.celk.util.CollectionUtils.isNotNullEmpty(exameSolicitadoList) && exameSolicitadoList.contains(codigoExame)) {
                                continue;
                            }
                            et = new ExameThrift();
                            solicitadoList = new ArrayList<>();
                            solicitadoList.add("A");
                            et.setCodigoExame(codigoExame);
                            et.setSolicitadoAvaliado(solicitadoList);
                            et.setCodigoExameIsSet(true);
                            et.setSolicitadoAvaliadoIsSet(true);
                            exameThriftList.add(et);
                        }
                    }
                    exameThriftList.addAll(getOutrosSia(carregarFichasEsusItemProcedimento(item.getCodigo(), dto.getItemProcedimentoList())));
                    faict.setExame(exameThriftList);


                    if (item.getRacionalidadeSaude() != null) {
                        faict.setPic(item.getRacionalidadeSaude());
                    }
                    faict.setFicouEmObservacao(RepositoryComponentDefault.SIM_LONG.equals(item.getFicouEmObservacao()));
                    faict.setCondutas(getConduta(item.getCondutas()));
                    if (EsusFichaAtendIndividualItem.SEXO_FEMININO.equals(item.getSexo())) {
                        if (item.getGravidezPlanejada() != null) {
                            faict.setStGravidezPlanejada(RepositoryComponentDefault.SIM_LONG.equals(item.getGravidezPlanejada()));
                        }
                        if (item.getNumeroGestasPrevias() != null) {
                            faict.setNuGestasPrevias(item.getNumeroGestasPrevias().intValue());
                        }
                        if (item.getNumeroPartos() != null) {
                            faict.setNuPartos(item.getNumeroPartos().intValue());
                        }
                    }
                    atendimentos.add(item);
                    lstFaict.add(faict);
                }

                while (!lstFaict.isEmpty()) {
                    if (lstFaict.size() > 12) {
                        List<EsusIntegracaoCds> esusIntegracaoCdsListSplit = esusIntegracaoCdsList.subList(0, 12);
                        List<FichaAtendimentoIndividualChildThrift> lstCopia = lstFaict.subList(0, 12);
                        List<EsusFichaAtendIndividualItem> lstCopiaAtend = atendimentos.subList(0, 12);
                        criarCabecalho(esusIntegracaoCdsListSplit, empresa, unidadeCentral, profissional, lstCopia, lstCopiaAtend);
                        lstFaict.removeAll(lstCopia);
                        atendimentos.removeAll(lstCopiaAtend);
                        esusIntegracaoCdsList.removeAll(esusIntegracaoCdsListSplit);
                    } else {
                        criarCabecalho(esusIntegracaoCdsList, empresa, unidadeCentral, profissional, lstFaict, atendimentos);
                        lstFaict.removeAll(lstFaict);
                        atendimentos.clear();
                        esusIntegracaoCdsList.clear();
                    }
                }
            }
        }

        return registros.size();
    }

    public void criarCabecalho(List<EsusIntegracaoCds> esusIntegracaoCdsList, Empresa empresa, Empresa unidadeCentral, Profissional profissional, List<FichaAtendimentoIndividualChildThrift> lstFaict, List<EsusFichaAtendIndividualItem> lstAtendIndividual) throws DAOException, ValidacaoException {
        EsusIntegracaoCds esusIntegracaoCds = esusIntegracaoCdsList.get(0);

        DadoTransporteThrift dtt = new DadoTransporteThrift();
        FichaAtendimentoIndividualMasterThrift faim = new FichaAtendimentoIndividualMasterThrift();
        String uuid;
        if (esusIntegracaoCds.getUuid() != null) {
            uuid = esusIntegracaoCds.getUuid();
        } else {
            uuid = UUID.randomUUID().toString();
        }
        faim.setUuidFicha(uuid);
        faim.setTpCdsOrigem(3); //terceiros

        VariasLotacoesHeaderThrift variasLotacoesHeaderThrift = new VariasLotacoesHeaderThrift();
        LotacaoHeaderThrift unicaLotacaoHeaderThrift = new LotacaoHeaderThrift();
        unicaLotacaoHeaderThrift.setProfissionalCNS(profissional.getCodigoCns());
        unicaLotacaoHeaderThrift.setCboCodigo_2002(esusIntegracaoCds.getEsusFichaAtendIndividual().getCboPrincipal().getCbo());
        unicaLotacaoHeaderThrift.setCnes(empresa.getCnes());

        if (esusIntegracaoCds.getEsusFichaAtendIndividual().getCodigoIne() != null) {
            String ine = StringUtils.leftPad(esusIntegracaoCds.getEsusFichaAtendIndividual().getCodigoIne(), 10, '0');
            unicaLotacaoHeaderThrift.setIne(ine);
        }

        variasLotacoesHeaderThrift.setLotacaoFormPrincipal(unicaLotacaoHeaderThrift);

        Profissional profissionalSecundario = esusIntegracaoCds.getEsusFichaAtendIndividual().getProfissionalSecundario();
        if (profissionalSecundario != null) {
            LotacaoHeaderThrift unicaLotacaoHeaderThrift2 = new LotacaoHeaderThrift();
            unicaLotacaoHeaderThrift2.setProfissionalCNS(profissionalSecundario.getCodigoCns());
            unicaLotacaoHeaderThrift2.setProfissionalCNSIsSet(true);
            unicaLotacaoHeaderThrift2.setCnes(empresa.getCnes());

            TabelaCbo cboSecundario = esusIntegracaoCds.getEsusFichaAtendIndividual().getCboSecundario();
            if (cboSecundario != null && cboSecundario.getCbo() != null) {
                unicaLotacaoHeaderThrift2.setCboCodigo_2002(cboSecundario.getCbo());
                unicaLotacaoHeaderThrift2.setCboCodigo_2002IsSet(true);
            }
            variasLotacoesHeaderThrift.setLotacaoFormAtendimentoCompartilhado(unicaLotacaoHeaderThrift2);
        }

        variasLotacoesHeaderThrift.setDataAtendimento(esusIntegracaoCds.getEsusFichaAtendIndividual().getDataAtendimento().getTime());
        variasLotacoesHeaderThrift.setCodigoIbgeMunicipio(EnderecoHelper.getCodigoIbgeComDV(empresa.getCidade().getCodigo()).toString());
        faim.setHeaderTransport(variasLotacoesHeaderThrift);

        faim.setAtendimentosIndividuais(lstFaict);

        dtt.setUuidDadoSerializado(uuid); //verificar
        dtt.setTipoDadoSerializado(IExportacaoEsus.TipoDadoSerializadoEsus.FICHA_ATENDIMENTO_INDIVIDUAL.getValue());
        dtt.setCnesDadoSerializado(empresa.getCnes());
        dtt.setCodIbge(EnderecoHelper.getCodigoIbgeComDV(empresa.getCidade().getCodigo()).toString());

        if (esusIntegracaoCds.getEsusFichaAtendIndividual().getCodigoIne() != null) {
            dtt.setIneDadoSerializado(esusIntegracaoCds.getEsusFichaAtendIndividual().getCodigoIne());
        }
        //dtt.setNumLote(null); //verificar
        try {
            byte[] serialize = serialize(faim);
            dtt.setDadoSerializado(serialize);
        } catch (TException ex) {
            throw new ValidacaoException(ex);
        }

        dtt.setRemetente(IntegracaoCdsHelper.getDadoInstalacaoThriftRefatorado(unidadeCentral));
        dtt.setOriginadora(IntegracaoCdsHelper.getDadoInstalacaoThriftRefatorado(empresa));
        dtt.setVersao(IntegracaoCdsHelper.getVersaoThrift(5,6,1));

        dadosTransporteThrift.add(dtt);

        IntegracaoCdsHelper.updateEsusIntegracaoCds(esusIntegracaoCdsList, uuid, null, getSession(), param);
        hasIntegratedRecord = true;
    }

    public List<EsusFichaAtendIndividualItem> carregarFichasEsusItem(Long codigoEfai, List<EsusFichaAtendIndividualItem> list) {
        List<EsusFichaAtendIndividualItem> lstEsusFichaAtendIndividualItem = Lambda.select(list, Lambda.having(on(EsusFichaAtendIndividualItem.class).getEsusFichaAtendIndividual().getCodigo(), Matchers.equalTo(codigoEfai)));
        return lstEsusFichaAtendIndividualItem;
    }

    public List<EsusFichaAtendIndividualItemProcedimento> carregarFichasEsusItemProcedimento(Long codigoEfaiItem, List<EsusFichaAtendIndividualItemProcedimento> list) {
        List<EsusFichaAtendIndividualItemProcedimento> lstEsusFichaAtendIndividualItemProcedimento
                = Lambda.select(list, Lambda.having(on(EsusFichaAtendIndividualItemProcedimento.class).getEsusFichaAtendIndividualItem().getCodigo(), Matchers.equalTo(codigoEfaiItem)));
        return lstEsusFichaAtendIndividualItemProcedimento;
    }

    public List<String> getExameSolicitado(List<EsusFichaAtendIndividualItemProcedimento> lstEsusFichaAtendIndividualItemProcedimento) {
        List<EsusFichaAtendIndividualItemProcedimento> lstEsusFichaAtendIndividualItemProcedimentoAux
                = Lambda.select(lstEsusFichaAtendIndividualItemProcedimento, Lambda.having(Lambda.on(EsusFichaAtendIndividualItemProcedimento.class).getSolicitadoAvaliado(), Matchers.equalTo(EsusFichaAtendIndividualItemProcedimento.AvaliadoSolicitado.SOLICITADO.value())));

        List<String> lstExameSolicitado = new ArrayList();
        for (EsusFichaAtendIndividualItemProcedimento itemProcedimento : lstEsusFichaAtendIndividualItemProcedimentoAux) {
            if (itemProcedimento.getExameEsus() != null) {
                lstExameSolicitado.add(itemProcedimento.getExameEsus().getCodigoEsus());
            }
        }
        return lstExameSolicitado;
    }

    public ProblemaCondicaoAvaliacaoAIThrift getProblemaCondicaoAvaliada(EsusFichaAtendIndividualItem item) throws ValidacaoException {
        List<Long> lstResolvida = Valor.resolveSomatorio(item.getProblemaCondicaoAvaliada());
        ProblemaCondicaoAvaliacaoAIThrift problemaCondicaoAvaliacaoAIThrift = new ProblemaCondicaoAvaliacaoAIThrift();
        EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada problemaCondicaoAvaliada;

        for (Long dado : lstResolvida) {
            problemaCondicaoAvaliada = EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.valeuOf(dado);
            if (problemaCondicaoAvaliada == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_erro_problema_condicao_avaliada", item.getAtendimento().getCodigo()));
            }

            problemaCondicaoAvaliacaoAIThrift.addToCiaps((String) problemaCondicaoAvaliada.value());
        }
        if (item.getCiap() != null && item.getCiap().getReferencia() != null && item.getCiap().getReferencia().length() == 3) {
            String ciapRelacaoCodigoAB;
            if (item.getCiap().getCodigoEsus() != null) {
                ciapRelacaoCodigoAB = item.getCiap().getCodigoEsus();
            } else {
                ciapRelacaoCodigoAB = CiapHelper.getClassificacaoAtendimentoCodigoAB(item.getCiap().getReferencia());
                if (ciapRelacaoCodigoAB.isEmpty()) {
                    ciapRelacaoCodigoAB = item.getCiap().getReferencia();
                }
            }
            if (String.valueOf(item.getCiap().getReferencia().charAt(0)).equals("-") || (item.getCiap().getCodigoEsus() != null && String.valueOf(item.getCiap().getCodigoEsus().charAt(0)).equals("-"))) {
                problemaCondicaoAvaliacaoAIThrift.setOutroCiap1(item.getCiap().getCodigoEsus() != null ? item.getCiap().getCodigoEsus().replace("-", "") : item.getCiap().getReferencia().replace("-", ""));
            } else {
                boolean ciapAdicionado = false;
                if (CollectionUtils.isNotNullEmpty(lstResolvida)) {
                    for (Long dado : lstResolvida) {
                        problemaCondicaoAvaliada = EsusFichaAtendIndividualItem.ProblemaCondicaoAvaliada.valeuOf(dado);
                        if (problemaCondicaoAvaliada == null) {
                            throw new ValidacaoException(Bundle.getStringApplication("msg_erro_problema_condicao_avaliada", item.getAtendimento().getCodigo()));
                        }

                        if (CiapHelper.getClassificacaoAtendimentoCodigoAB(item.getCiap().getReferencia()).equals(problemaCondicaoAvaliada.value())) {
                            ciapAdicionado = true;
                            break;
                        }
                    }
                }
                if (!ciapAdicionado) {
                    problemaCondicaoAvaliacaoAIThrift.setOutroCiap1(ciapRelacaoCodigoAB);
                }
            }
        }
        if (item.getCiap2() != null && item.getCiap2().getReferencia() != null && item.getCiap2().getReferencia().length() == 3) {
            if (String.valueOf(item.getCiap2().getReferencia().charAt(0)).equals("-") || (item.getCiap2().getCodigoEsus() != null && String.valueOf(item.getCiap2().getCodigoEsus().charAt(0)).equals("-"))) {
                problemaCondicaoAvaliacaoAIThrift.setOutroCiap2(item.getCiap2().getCodigoEsus() != null ? item.getCiap2().getCodigoEsus().replace("-", "") : item.getCiap2().getReferencia().replace("-", ""));
            } else {
                problemaCondicaoAvaliacaoAIThrift.setOutroCiap2(item.getCiap2().getCodigoEsus() != null ? item.getCiap2().getCodigoEsus() : item.getCiap2().getReferencia());
            }
        }

        if (item.getCid() != null) {
            problemaCondicaoAvaliacaoAIThrift.setCid10(item.getCid().getCodigo().trim());
        }

        if (item.getCid2() != null) {
            problemaCondicaoAvaliacaoAIThrift.setCid10_2(item.getCid2().getCodigo().trim());
        }

        return problemaCondicaoAvaliacaoAIThrift;
    }

    public List<Long> getConduta(Long valor) {
        List<Long> lstResolvida = Valor.resolveSomatorio(valor);
        List<Long> lst = new ArrayList<>();
        EsusFichaAtendIndividualItem.CondutaEncaminhamento condutaEncaminhamento;

        for (Long dado : lstResolvida) {
            condutaEncaminhamento = EsusFichaAtendIndividualItem.CondutaEncaminhamento.valeuOf(dado);
            if (condutaEncaminhamento != null) {
                lst.add((Long) condutaEncaminhamento.value());
            }
        }
        return lst;
    }

    public List<Long> getNasf(Long valor) {
        List<Long> lstResolvida = Valor.resolveSomatorio(valor);
        List<Long> lst = new ArrayList<>();
        EsusFichaAtendIndividualItem.Nasf nasf;

        for (Long dado : lstResolvida) {
            nasf = EsusFichaAtendIndividualItem.Nasf.valeuOf(dado);
            if (nasf != null) {
                lst.add((Long) nasf.value());
            }
        }
        return lst;
    }

    public List<String> getExameAvaliado(List<EsusFichaAtendIndividualItemProcedimento> lstEsusFichaAtendIndividualItemProcedimento) {
        List<EsusFichaAtendIndividualItemProcedimento> lstEsusFichaAtendIndividualItemProcedimentoAux
                = Lambda.select(lstEsusFichaAtendIndividualItemProcedimento, Lambda.having(Lambda.on(EsusFichaAtendIndividualItemProcedimento.class).getSolicitadoAvaliado(), Matchers.equalTo(EsusFichaAtendIndividualItemProcedimento.AvaliadoSolicitado.AVALIADO.value())));
        //lstEsusFichaAtendIndividualItemProcedimentoAux.addAll(Lambda.select(lstEsusFichaAtendIndividualItemProcedimento, Lambda.having(Lambda.on(EsusFichaAtendIndividualItemProcedimento.class).getSolicitadoAvaliado(), Matchers.equalTo(EsusFichaAtendIndividualItemProcedimento.AvaliadoSolicitado.AMBOS.value()))));

        List<String> lstExameAvaliado = new ArrayList();
        for (EsusFichaAtendIndividualItemProcedimento itemProcedimento : lstEsusFichaAtendIndividualItemProcedimentoAux) {
            if (itemProcedimento.getExameEsus() != null) {
                lstExameAvaliado.add(itemProcedimento.getExameEsus().getCodigoEsus());
            }
        }
        return lstExameAvaliado;
    }

    private String validarExamesEsusAvaliadosSolicitados(List<EsusFichaAtendIndividualItemProcedimento> lstEsusFichaAtendIndividualItemProcedimento, EsusValidacoesFichasDTOParam paramValidacao) throws ValidacaoException {
        String inconsistencia = "";
        List<EsusFichaAtendIndividualItemProcedimento> lstEsusFichaAtendIndividualItemProcedimentoAux
                = Lambda.select(lstEsusFichaAtendIndividualItemProcedimento, Lambda.having(on(EsusFichaAtendIndividualItemProcedimento.class).getExameEsus(), Matchers.notNullValue()));

        if (CollectionUtils.isNotNullEmpty(lstEsusFichaAtendIndividualItemProcedimentoAux)) {
            for (EsusFichaAtendIndividualItemProcedimento itemProcedimento : lstEsusFichaAtendIndividualItemProcedimentoAux) {
                //paramValidacao.setEsusFichaAtendIndividualItemProcedimento(itemProcedimento);
                EsusFichaAtendIndividualItemProcedimento item = (EsusFichaAtendIndividualItemProcedimento) getSessionLeitura().get(EsusFichaAtendIndividualItemProcedimento.class, itemProcedimento.getCodigo());
                paramValidacao.setEsusFichaAtendIndividualItemProcedimento(item);
                inconsistencia += EsusValidacoesFichaAtendimentoIndividualItemProcedimentoHelper.validarExamesEsusSolicitadosAvaliados(paramValidacao);
            }
        }
        return inconsistencia;
    }

    private String validarOutrosSia(List<EsusFichaAtendIndividualItemProcedimento> lstEsusFichaAtendIndividualItemProcedimento, EsusValidacoesFichasDTOParam paramValidacao) throws ValidacaoException {
        StringBuilder sb = new StringBuilder();
        List<EsusFichaAtendIndividualItemProcedimento> lstEsusFichaAtendIndividualItemProcedimentoAux
                = Lambda.select(lstEsusFichaAtendIndividualItemProcedimento, Lambda.having(on(EsusFichaAtendIndividualItemProcedimento.class).getExameEsus(), Matchers.nullValue()));

        if (CollectionUtils.isNotNullEmpty(lstEsusFichaAtendIndividualItemProcedimentoAux)) {
            for (EsusFichaAtendIndividualItemProcedimento itemProcedimento : lstEsusFichaAtendIndividualItemProcedimentoAux) {
                //paramValidacao.setEsusFichaAtendIndividualItemProcedimento(itemProcedimento);
                EsusFichaAtendIndividualItemProcedimento item = (EsusFichaAtendIndividualItemProcedimento) getSessionLeitura().get(EsusFichaAtendIndividualItemProcedimento.class, itemProcedimento.getCodigo());
                paramValidacao.setEsusFichaAtendIndividualItemProcedimento(item);
                sb.append(EsusValidacoesFichaAtendimentoIndividualItemProcedimentoHelper.validarProcedimentoSia(paramValidacao));
            }
        }
        return sb.toString();
    }

    public List<ExameThrift> getOutrosSia(List<EsusFichaAtendIndividualItemProcedimento> lstEsusFichaAtendIndividualItemProcedimento) {
        List<EsusFichaAtendIndividualItemProcedimento> lstEsusFichaAtendIndividualItemProcedimentoAux
                = Lambda.select(lstEsusFichaAtendIndividualItemProcedimento, Lambda.having(Lambda.on(EsusFichaAtendIndividualItemProcedimento.class).getExameEsus(), Matchers.nullValue()));

        List<EsusFichaAtendIndividualItemProcedimento> lstSolicitados
                = Lambda.select(lstEsusFichaAtendIndividualItemProcedimentoAux, Lambda.having(Lambda.on(EsusFichaAtendIndividualItemProcedimento.class).getSolicitadoAvaliado(), Matchers.equalTo(EsusFichaAtendIndividualItemProcedimento.AvaliadoSolicitado.SOLICITADO.value())));

        List<EsusFichaAtendIndividualItemProcedimento> lstAvaliados
                = Lambda.select(lstEsusFichaAtendIndividualItemProcedimentoAux, Lambda.having(Lambda.on(EsusFichaAtendIndividualItemProcedimento.class).getSolicitadoAvaliado(), Matchers.equalTo(EsusFichaAtendIndividualItemProcedimento.AvaliadoSolicitado.AVALIADO.value())));

        List<ExameThrift> lstOutros = new ArrayList();


        if (br.com.celk.util.CollectionUtils.isNotNullEmpty(lstSolicitados)) {
            for (EsusFichaAtendIndividualItemProcedimento procedimentoSolicitado : lstSolicitados) {
                ExameThrift ost = new ExameThrift();
                List<String> solicitadoList = new ArrayList<>();
                if (CollectionUtils.isNotNullEmpty(lstAvaliados)) {
                    for (EsusFichaAtendIndividualItemProcedimento procedimentoAvaliado : lstAvaliados) {
                        if (procedimentoAvaliado.getProcedimento() != null && procedimentoSolicitado.getProcedimento() != null && procedimentoSolicitado.getProcedimento().getCodigo().equals(procedimentoAvaliado.getProcedimento().getCodigo())) {
                            solicitadoList.add("A");
                            break;
                        }
                    }
                }
                solicitadoList.add("S");
                ost.setCodigoExame(procedimentoSolicitado.getProcedimento().getReferencia());
                ost.setSolicitadoAvaliado(solicitadoList);
                lstOutros.add(ost);
            }
        }

        if (br.com.celk.util.CollectionUtils.isNotNullEmpty(lstAvaliados)) {
            for (EsusFichaAtendIndividualItemProcedimento procedimentoAvaliado : lstAvaliados) {
                if (br.com.celk.util.CollectionUtils.isNotNullEmpty(lstSolicitados)) {
                    boolean jaAdicionado = false;
                    if (CollectionUtils.isNotNullEmpty(lstSolicitados)) {
                        for (EsusFichaAtendIndividualItemProcedimento procedimentoSolicitado : lstSolicitados) {
                            if (procedimentoAvaliado.getProcedimento() != null && procedimentoSolicitado.getProcedimento() != null && procedimentoSolicitado.getProcedimento().getCodigo().equals(procedimentoAvaliado.getProcedimento().getCodigo())) {
                                jaAdicionado = true;
                                break;
                            }
                        }
                    }
                    if (jaAdicionado) {
                        continue;
                    }
                    ExameThrift ost = new ExameThrift();
                    ost.setCodigoExame(procedimentoAvaliado.getProcedimento().getReferencia());
                    ost.setSolicitadoAvaliado(Arrays.asList("A"));
                    lstOutros.add(ost);
                }
            }
        }
        return lstOutros;
    }

    private boolean isCiapValido(Ciap ciap) throws DAOException, ValidacaoException {
        return BOFactory.getBO(BasicoFacade.class).isCiapValido(ciap);
    }
}
