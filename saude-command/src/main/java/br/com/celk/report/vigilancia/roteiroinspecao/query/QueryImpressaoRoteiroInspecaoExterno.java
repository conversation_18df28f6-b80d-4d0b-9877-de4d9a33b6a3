package br.com.celk.report.vigilancia.roteiroinspecao.query;

import br.com.celk.report.vigilancia.roteiroinspecao.dto.ImpressaoRoteiroInspecaoExternoDTO;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.ItemInspecao;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.ItemInspecaoPergunta;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RoteiroInspecao;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryImpressaoRoteiroInspecaoExterno extends CommandQuery implements ITransferDataReport<RoteiroInspecao, ImpressaoRoteiroInspecaoExternoDTO> {

    private RoteiroInspecao roteiroInspecao;
    private List<ImpressaoRoteiroInspecaoExternoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(ImpressaoRoteiroInspecaoExternoDTO.class.getName());

        hql.addToSelect("roteiroItemInspecao.codigo", "roteiroItemInspecao.codigo");

        hql.addToSelect("roteiroInspecao.codigo", "roteiroInspecao.codigo");
        hql.addToSelect("roteiroInspecao.nomeRoteiro", "roteiroInspecao.nomeRoteiro");
        hql.addToSelect("roteiroInspecao.enquadramentoLegal", "roteiroInspecao.enquadramentoLegal");
        hql.addToSelect("roteiroInspecao.observacaoInicial", "roteiroInspecao.observacaoInicial");
        hql.addToSelect("roteiroInspecao.observacaoFinal", "roteiroInspecao.observacaoFinal");

        hql.addToSelect("itemInspecao.codigo", "itemInspecao.codigo");
        hql.addToSelect("itemInspecao.subtitulo", "itemInspecao.subtitulo");
        hql.addToSelect("itemInspecao.enquadramentoLegal", "itemInspecao.enquadramentoLegal");

        hql.addToFrom("RoteiroItemInspecao roteiroItemInspecao "
                + " left join roteiroItemInspecao.roteiroInspecao roteiroInspecao "
                + " left join roteiroItemInspecao.itemInspecao itemInspecao "
                + " left join roteiroInspecao.atividadeEstabelecimento atividadeEstabelecimento "
        );
        hql.addToWhereWhithAnd("roteiroItemInspecao.roteiroInspecao = ", roteiroInspecao);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(this.result)) {
            for (ImpressaoRoteiroInspecaoExternoDTO listResult : result) {
                ImpressaoRoteiroInspecaoExternoDTO dto = listResult;
                ItemInspecao itemInspecao = listResult.getItemInspecao();
                String enquadramentoLegal = listResult.getItemInspecao().getEnquadramentoLegal();

                List<ItemInspecaoPergunta> listPerguntas = LoadManager.getInstance(ItemInspecaoPergunta.class)
                        .addProperties(new HQLProperties(ItemInspecaoPergunta.class).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(ItemInspecaoPergunta.PROP_ITEM_INSPECAO, itemInspecao))
                        .start().getList();

                if (CollectionUtils.isNotNullEmpty(listPerguntas)) {
                    List<ItemInspecaoPergunta> itemInspecaoPerguntas = new ArrayList<>();
                    for (ItemInspecaoPergunta pergunta : listPerguntas) {
                        StringBuilder sb = new StringBuilder();
                        sb.append(enquadramentoLegal == null ? "" : enquadramentoLegal).append(" ").append(pergunta.getLeiArtigo() == null ? "" : pergunta.getLeiArtigo());
                        pergunta.setLeiArtigo(sb.toString());
                        itemInspecaoPerguntas.add(pergunta);
                    }
                    dto.setItemInspecaoPerguntaList(itemInspecaoPerguntas);
                }
            }
        }
    }

    @Override
    public List<ImpressaoRoteiroInspecaoExternoDTO> getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public void setDTOParam(RoteiroInspecao param) {
        this.roteiroInspecao = param;
    }
}
