package br.com.celk.report.materiais.brasindice.query;

import br.com.celk.report.materiais.brasindice.interfaces.dto.RelacaoProdutoBrasindiceDTO;
import br.com.celk.report.materiais.brasindice.interfaces.dto.RelacaoProdutoBrasindiceDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioRelacaoProdutoBrasindice extends CommandQuery<QueryRelatorioRelacaoProdutoBrasindice> implements ITransferDataReport<RelacaoProdutoBrasindiceDTOParam, RelacaoProdutoBrasindiceDTO> {

    private RelacaoProdutoBrasindiceDTOParam param;
    private Collection<RelacaoProdutoBrasindiceDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(RelacaoProdutoBrasindiceDTO.class.getName());

        hql.addToSelect("p.codigo", "codigoProduto");
        hql.addToSelect("p.descricao", "descricaoProduto");
        
        hql.addToSelect("pb.codigo", "codigoBrasindice");
        hql.addToSelect("pb.descricaoMedicamento", "descricaoBrasindice");
        hql.addToSelect("pb.descricaoApresentacao", "descricaoApresentacao");
        hql.addToSelect("pb.descricaoLaboratorio", "descricaoLaboratorio");
        hql.addToSelect("pb.codigoTiss", "codigoTiss");
        hql.addToSelect("pb.codigoTuss", "codigoTuss");
        
        hql.addToSelect("pbi.preco", "preco");
        hql.addToSelect("pbi.versaoBrasindice", "versaoBrasindice");
        hql.addToSelect("pbi.dataInicioVigencia", "dataInicioVigencia");

        hql.addToFrom("ProdutoBrasindiceItem pbi "
                + " left join pbi.produtoBrasindice pb ");
        
        hql.addToFrom("EloProdutoBrasindice epb "
                + " left join epb.produto p"
                + " left join p.subGrupo sg"
                + " join sg.roGrupoProduto gp");

        hql.addToWhereWhithAnd("epb.produtoBrasindice = pb");
        
        if(RelacaoProdutoBrasindiceDTOParam.TipoProduto.MATERIAIS.equals(this.param.getTipoProduto())){
            hql.addToWhereWhithAnd("sg.flagMedicamento = ", SubGrupo.MEDICAMENTO_NAO);
        } else if(RelacaoProdutoBrasindiceDTOParam.TipoProduto.MEDICAMENTOS.equals(this.param.getTipoProduto())){
            hql.addToWhereWhithAnd("sg.flagMedicamento = ", SubGrupo.MEDICAMENTO_SIM);
        }
        
        hql.addToWhereWhithAnd("gp = ",this.param.getGrupoProduto());

        if (this.param.getSubGrupo() != null) {
            hql.addToWhereWhithAnd("sg.id.codigo = ",this.param.getSubGrupo().getId().getCodigo());
        }
        
        hql.addToWhereWhithAnd("pbi.tipoPreco = :tipoPreco");
        
        hql.addToOrder(" p.descricao asc");
    }
    
    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        if(RelacaoProdutoBrasindiceDTOParam.TipoPreco.PMC.equals(this.param.getTipoPreco())){
            query.setParameter("tipoPreco", "PMC");
        } else if(RelacaoProdutoBrasindiceDTOParam.TipoPreco.PFB.equals(this.param.getTipoPreco())){
            query.setParameter("tipoPreco", "PFB");
        }
    }
    
    @Override
    public void setDTOParam(RelacaoProdutoBrasindiceDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection<RelacaoProdutoBrasindiceDTO> getResult() {
        return this.result;
    }
}