package br.com.celk.report.vigilancia.cva.query;

import br.com.celk.report.vigilancia.cva.dto.RelatorioRelacaoMicrochipagemDTO;
import br.com.celk.report.vigilancia.cva.dto.RelatorioRelacaoMicrochipagemDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelacaoMicrochipagem extends CommandQuery implements ITransferDataReport<RelatorioRelacaoMicrochipagemDTOParam, RelatorioRelacaoMicrochipagemDTO> {

    private RelatorioRelacaoMicrochipagemDTOParam param;
    private List<RelatorioRelacaoMicrochipagemDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("count(cvaAnimal.codigo)", "quantidade");

        if (RelatorioRelacaoMicrochipagemDTOParam.FormaApresentacao.BAIRRO.equals(this.param.getFormaApresentacao())) {
            hql.addToSelectAndGroupAndOrder("vigilanciaEndereco.bairro", "bairro");
        } else if (RelatorioRelacaoMicrochipagemDTOParam.FormaApresentacao.SITUACAO.equals(this.param.getFormaApresentacao())) {
            hql.addToSelectAndGroupAndOrder("cvaAnimal.status", "cvaAnimal.status");
        } else if (RelatorioRelacaoMicrochipagemDTOParam.FormaApresentacao.TIPO_ANIMAL.equals(this.param.getFormaApresentacao())) {
            hql.addToSelectAndGroupAndOrder("cvaAnimal.tipoAnimal", "cvaAnimal.tipoAnimal");
        }

        hql.addToSelectAndGroupAndOrder("especieAnimal.codigo", "especieAnimal.codigo");
        hql.addToSelectAndGroupAndOrder("especieAnimal.descricao", "especieAnimal.descricao");
        hql.addToSelectAndGroupAndOrder("cvaAnimal.sexo", "cvaAnimal.sexo");

        hql.setTypeSelect(RelatorioRelacaoMicrochipagemDTO.class.getName());
        hql.setConvertToLeftJoin(true);

        hql.addToFrom("CvaAnimal cvaAnimal"
                + " left join cvaAnimal.cvaRacaAnimal cvaRacaAnimal"
                + " left join cvaRacaAnimal.especieAnimal especieAnimal"
                + " left join cvaAnimal.cvaProprietarioResponsavel proprietarioResponsavel"
                + " left join proprietarioResponsavel.vigilanciaEndereco vigilanciaEndereco");

        hql.addToWhereWhithAnd("vigilanciaEndereco.pais = ", this.param.getPais());
        hql.addToWhereWhithAnd("vigilanciaEndereco.cidade.estado = ", this.param.getEstado());
        hql.addToWhereWhithAnd("vigilanciaEndereco.cidade = ", this.param.getCidade());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("vigilanciaEndereco.bairro", this.param.getBairro()));
        hql.addToWhereWhithAnd("cvaAnimal.tipoAnimal = ", this.param.getTipo());
        hql.addToWhereWhithAnd("cvaAnimal.status = ", this.param.getSituacao());
        hql.addToWhereWhithAnd("especieAnimal = ", this.param.getEspecie());
        hql.addToWhereWhithAnd("cvaAnimal.sexo = ", this.param.getSexo());

        if (RepositoryComponentDefault.SIM.equals(this.param.getMicrochipagem())) {
            hql.addToWhereWhithAnd("cvaAnimal.numeroMicrochip is not null");
        } else if (RepositoryComponentDefault.NAO.equals(this.param.getMicrochipagem())) {
            hql.addToWhereWhithAnd("cvaAnimal.numeroMicrochip is null");
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioRelacaoMicrochipagemDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioRelacaoMicrochipagemDTOParam param) {
        this.param = param;
    }

}
