package br.com.celk.bo.agenda.manutencaoagenda;

import br.com.celk.bo.agenda.interfaces.dto.ManutencaoAgendaHorarioDTO;
import br.com.celk.bo.agenda.interfaces.dto.ManutencaoAgendaHorarioDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.Data;
import java.util.List;
import org.hibernate.Query;
import org.joda.time.DateTime;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultarHorariosManutencaoAgendaHorario extends CommandQuery<QueryConsultarHorariosManutencaoAgendaHorario> {

    private List<ManutencaoAgendaHorarioDTO> list;
    private ManutencaoAgendaHorarioDTOParam param;
    private Long count;

    public QueryConsultarHorariosManutencaoAgendaHorario(ManutencaoAgendaHorarioDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        if(param.isCount()){
            hql.addToSelect("count(*)");
        } else {
            hql.addToSelect("agh.codigo", "agendaGradeHorario.codigo");
            hql.addToSelect("agh.status", "agendaGradeHorario.status");
            hql.addToSelect("agh.hora", "agendaGradeHorario.hora");
            hql.addToSelect("agh.version", "agendaGradeHorario.version");

            hql.addToSelect("aga.codigo", "agendaGradeHorario.agendaGradeAtendimento.codigo");
            hql.addToSelect("aga.quantidadeAtendimento", "agendaGradeHorario.agendaGradeAtendimento.quantidadeAtendimento");
            hql.addToSelect("aga.tempoMedio", "agendaGradeHorario.agendaGradeAtendimento.tempoMedio");
            hql.addToSelect("aga.quantidadeCotaUnidade", "agendaGradeHorario.agendaGradeAtendimento.quantidadeCotaUnidade");
            hql.addToSelect("aga.quantidadeAtendimentoOriginal", "agendaGradeHorario.agendaGradeAtendimento.quantidadeAtendimentoOriginal");
            hql.addToSelect("aga.version", "agendaGradeHorario.agendaGradeAtendimento.version");
            hql.addToSelect("coalesce(aga.quantidadeAtendimentoBloqueado, 0)", "agendaGradeHorario.agendaGradeAtendimento.quantidadeAtendimentoBloqueado");

            hql.addToSelect("taa.codigo", "agendaGradeHorario.agendaGradeAtendimento.tipoAtendimentoAgenda.codigo");
            hql.addToSelect("taa.descricao", "agendaGradeHorario.agendaGradeAtendimento.tipoAtendimentoAgenda.descricao");

            hql.addToSelect("ag.codigo", "agendaGradeHorario.agendaGradeAtendimento.agendaGrade.codigo");
            hql.addToSelect("ag.data", "agendaGradeHorario.agendaGradeAtendimento.agendaGrade.data");
            hql.addToSelect("ag.horaInicial", "agendaGradeHorario.agendaGradeAtendimento.agendaGrade.horaInicial");
            hql.addToSelect("ag.horaFinal", "agendaGradeHorario.agendaGradeAtendimento.agendaGrade.horaFinal");
            hql.addToSelect("ag.version", "agendaGradeHorario.agendaGradeAtendimento.agendaGrade.version");

            hql.addToSelect("a.codigo", "agendaGradeHorario.agendaGradeAtendimento.agendaGrade.agenda.codigo");
            hql.addToSelect("a.status", "agendaGradeHorario.agendaGradeAtendimento.agendaGrade.agenda.status");
            hql.addToSelect("a.version", "agendaGradeHorario.agendaGradeAtendimento.agendaGrade.agenda.version");

            hql.addToSelect("tp.codigo", "agendaGradeHorario.agendaGradeAtendimento.agendaGrade.agenda.tipoProcedimento.codigo");
            hql.addToSelect("tp.flagValidaHorario", "agendaGradeHorario.agendaGradeAtendimento.agendaGrade.agenda.tipoProcedimento.flagValidaHorario");

            hql.addToSelect("tpc.codigo", "agendaGradeHorario.agendaGradeAtendimento.agendaGrade.agenda.tipoProcedimento.tipoProcedimentoClassificacao.codigo");

            hql.addToSelect("p.codigo", "agendaGradeHorario.agendaGradeAtendimento.agendaGrade.agenda.profissional.codigo");
            hql.addToSelect("p.nome", "agendaGradeHorario.agendaGradeAtendimento.agendaGrade.agenda.profissional.nome");

            hql.addToSelect("e.codigo", "agendaGradeHorario.agendaGradeAtendimento.agendaGrade.agenda.empresa.codigo");
            hql.addToSelect("e.descricao", "agendaGradeHorario.agendaGradeAtendimento.agendaGrade.agenda.empresa.descricao");

            hql.setTypeSelect(ManutencaoAgendaHorarioDTO.class.getName());
        }

        hql.addToFrom("AgendaGradeHorario agh"
                + " left join agh.agendaGradeAtendimento aga"
                + " left join aga.tipoAtendimentoAgenda taa"
                + " left join aga.agendaGrade ag"
                + " left join ag.agenda a"
                + " left join a.tipoProcedimento tp"
                + " left join tp.tipoProcedimentoClassificacao tpc"
                + " left join a.profissional p"
                + " left join a.empresa e");

        hql.addToWhereWhithAnd("a.codigo = ", param.getCodigoAgenda());
        hql.addToWhereWhithAnd("ag.data", Data.adjustRangeHour(this.param.getPeriodo()));
        if(param.getSituacao() != null){
            hql.addToWhereWhithAnd("agh.status = ", param.getSituacao());            
        }
        if(param.getHorario() != null){
            hql.addToWhereWhithAnd("to_char(agh.hora, 'HH24:MI') =", new DateTime(param.getHorario()).toString("HH:mm"));
        }
        if(param.getTipoAtendimentoAgenda() != null){
            hql.addToWhereWhithAnd("taa.codigo = ", param.getTipoAtendimentoAgenda().getCodigo());            
        }
        hql.addToWhereWhithAnd("EXTRACT(DOW FROM ag.data) in ", param.getDiaSemanaSelecionadoList());
        
        if(!param.isCount()){
            hql.addToOrder("ag.data asc");
            hql.addToOrder("agh.hora asc");
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        if(param.isCount()){
            count = (Long)((List) result).get(0);
        } else {
            list = hql.getBeanList((List) result);
        }
    }
    
    @Override
    protected void customQuery(Query query) {
        if(param.isCount()){
            query.setMaxResults(1);
        }
    }
    
    public List<ManutencaoAgendaHorarioDTO> getList() {
        return list;
    }

    public Long getCountResults() {
        return count;
    }
    
}