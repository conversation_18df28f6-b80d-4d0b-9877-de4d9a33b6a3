package br.com.celk.bo.cadsus.exclusao;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryUsuarioCadsusExclusaoAgendamento extends CommandQuery {

    private final List<Long> codigoProcessoList;
    private List<AgendaGradeAtendimentoHorario> list;

    public QueryUsuarioCadsusExclusaoAgendamento(List<Long> codigoProcessoList) {
        this.codigoProcessoList = codigoProcessoList;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(AgendaGradeAtendimentoHorario.class.getName());

        hql.addToSelect("agah.codigo", "codigo");
        hql.addToSelect("agah.dataAgendamento", "dataAgendamento");

        hql.addToSelect("agah.dataCadastro", "dataCadastro");
        hql.addToSelect("agah.dataConfirmacaoPaciente", "dataConfirmacaoPaciente");
        hql.addToSelect("agah.status", "status");
        hql.addToSelect("agah.ordemAtendimento", "ordemAtendimento");
        hql.addToSelect("agah.version", "version");
        
        hql.addToSelect("aga.codigo", "agendaGradeAtendimento.codigo");
        
        hql.addToSelect("ag.codigo", "agendaGradeAtendimento.agendaGrade.codigo");
        
        hql.addToSelect("taa.codigo", "agendaGradeAtendimento.tipoAtendimentoAgenda.codigo");
        hql.addToSelect("taa.descricao", "agendaGradeAtendimento.tipoAtendimentoAgenda.descricao");
        hql.addToSelect("taa.tempoMedio", "agendaGradeAtendimento.tipoAtendimentoAgenda.tempoMedio");
        hql.addToSelect("taa.tipoAtendimento", "agendaGradeAtendimento.tipoAtendimentoAgenda.tipoAtendimento");
        
        hql.addToSelect("agh.codigo", "agendaGradeHorario.codigo");
        
        hql.addToSelect("tp.codigo", "tipoProcedimento.codigo");
        hql.addToSelect("tp.descricao", "tipoProcedimento.descricao");

        hql.addToSelect("la.codigo", "localAgendamento.codigo");
        hql.addToSelect("la.descricao", "localAgendamento.descricao");

        hql.addToSelect("p.codigo", "profissional.codigo");
        hql.addToSelect("p.nome", "profissional.nome");

        hql.addToSelect("ua.codigo", "unidadeAgendamento.codigo");
        hql.addToSelect("ua.descricao", "unidadeAgendamento.descricao");
        
        hql.addToSelect("u.codigo", "usuario.codigo");
        
        hql.addToSelect("sa.codigo", "solicitacaoAgendamento.codigo");

        hql.addToFrom("AgendaGradeAtendimentoHorario agah "
                + " left join agah.tipoProcedimento tp"
                + " left join agah.localAgendamento la"
                + " left join agah.profissional p"
                + " left join agah.unidadeAgendamento ua"
                + " left join agah.solicitacaoAgendamento sa"
                + " left join agah.usuario u"
                + " left join agah.agendaGradeAtendimento aga"
                + " left join aga.tipoAtendimentoAgenda taa"
                + " left join aga.agendaGrade ag"
                + " left join agah.agendaGradeHorario agh"
        );

        hql.addToWhereWhithAnd("agah.codigo in :codigoProcessoList");
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameterList("codigoProcessoList", codigoProcessoList);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public List<AgendaGradeAtendimentoHorario> getResult() {
        return this.list;
    }

}
