# Implementação de CAPTCHA na ConsultaMedicamentoPublicoPage

## Resumo

Foi implementado um sistema de CAPTCHA para prevenir consultas automatizadas de bots na página `ConsultaMedicamentoPublicoPage`. A implementação inclui:

- Geração de códigos CAPTCHA aleatórios
- Imagens com distorções e ruído
- Validação anti-bot
- Interface amigável com renovação de código

## Arquivos Criados

### 1. Utilitários Core
- `saude-web-wicket/src/main/java/br/com/celk/util/CaptchaGenerator.java`
  - Gera códigos CAPTCHA aleatórios
  - Cria imagens com distorções e ruído
  - Valida códigos informados pelo usuário

- `saude-web-wicket/src/main/java/br/com/celk/util/CaptchaSession.java`
  - Gerencia dados do CAPTCHA na sessão
  - Controla timeout (5 minutos)
  - Implementa uso único do código

### 2. Componente Wicket
- `saude-web-wicket/src/main/java/br/com/celk/component/captcha/CaptchaComponent.java`
  - Componente reutilizável para CAPTCHA
  - Integração com Wicket
  - Validação automática

- `saude-web-wicket/src/main/java/br/com/celk/component/captcha/CaptchaComponent.html`
  - Template HTML do componente
  - Interface amigável com botão de renovação

- `saude-web-wicket/src/main/java/br/com/celk/component/captcha/CaptchaImageResource.java`
  - Recurso para servir imagens CAPTCHA
  - Evita cache das imagens

### 3. Estilos
- `saude-web-wicket/src/main/java/br/com/celk/component/captcha/captcha.css`
  - Estilos CSS para o componente
  - Design responsivo

### 4. Testes
- `saude-web-wicket/src/test/java/br/com/celk/util/CaptchaGeneratorTest.java`
  - Testes unitários para validação

## Arquivos Modificados

### 1. ConsultaMedicamentoPublicoPage.java
**Alterações:**
- Adicionada importação: `import br.com.celk.component.captcha.CaptchaComponent;`
- Adicionado campo: `private CaptchaComponent captchaComponent;`
- Adicionado componente ao formulário
- Implementada validação no método `antesProcurar()`
- Implementada renovação no método `depoisProcurar()`

### 2. ConsultaMedicamentoPublicoPage.html
**Alterações:**
- Adicionado div para o componente CAPTCHA entre o campo de descrição e o botão procurar

## Como Funciona

1. **Geração**: Quando a página carrega, um código CAPTCHA de 5 caracteres é gerado
2. **Exibição**: Uma imagem com distorções é criada e exibida ao usuário
3. **Validação**: Antes de executar a consulta, o código digitado é validado
4. **Renovação**: Após cada tentativa (sucesso ou erro), um novo CAPTCHA é gerado
5. **Timeout**: Códigos expiram em 5 minutos para segurança

## Características de Segurança

- **Uso único**: Cada código só pode ser usado uma vez
- **Timeout**: Códigos expiram automaticamente
- **Distorções**: Imagens têm ruído e distorções para dificultar OCR
- **Case insensitive**: Aceita maiúsculas e minúsculas
- **Sessão**: Códigos são armazenados na sessão do usuário

## Benefícios

- **Anti-bot**: Previne consultas automatizadas
- **Reutilizável**: Componente pode ser usado em outras páginas
- **Configurável**: Fácil de personalizar (timeout, tamanho, etc.)
- **Acessível**: Interface amigável com opção de renovação
- **Performático**: Geração rápida de imagens

## Uso em Outras Páginas

Para usar o CAPTCHA em outras páginas:

```java
// 1. Adicionar o componente
private CaptchaComponent captchaComponent;

// 2. No onInitialize()
captchaComponent = new CaptchaComponent("captcha");
form.add(captchaComponent);

// 3. Validar antes da ação
try {
    captchaComponent.validateCaptcha();
    // Executar ação protegida
} catch (ValidacaoException e) {
    captchaComponent.refreshCaptcha(target);
    throw e;
}
```

```html
<!-- 4. No template HTML -->
<div wicket:id="captcha"></div>
```

## Configurações Opcionais

### Alterar timeout (CaptchaSession.java):
```java
private static final long CAPTCHA_TIMEOUT = 10 * 60 * 1000; // 10 minutos
```

### Alterar tamanho da imagem (CaptchaGenerator.java):
```java
private static final int IMAGE_WIDTH = 250;
private static final int IMAGE_HEIGHT = 80;
```

### Alterar comprimento do código (CaptchaGenerator.java):
```java
private static final int CAPTCHA_LENGTH = 6; // 6 caracteres
```

## Próximos Passos Sugeridos

1. **Testes**: Executar testes em ambiente de desenvolvimento
2. **Monitoramento**: Implementar logs para tentativas de CAPTCHA
3. **Métricas**: Acompanhar taxa de sucesso/falha
4. **Acessibilidade**: Considerar implementar versão em áudio
5. **Outras páginas**: Aplicar em outras páginas públicas se necessário

A implementação está completa e pronta para uso!
